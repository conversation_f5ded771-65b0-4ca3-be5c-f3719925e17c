<?php
// Model for handling demande-related database operations

class DemandeModel extends Model {

    // Get all demandes for a user
    public function getDemandesForUser($userId) {
        $stmt = $this->db->prepare("
            SELECT * FROM demandes_conges
            WHERE user_id = ?
            ORDER BY date_demande DESC
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }

    // Get all demandes for a user with filters, search, pagination and sorting
    public function getDemandesForUserWithFilters($userId, $period = 'all', $status = 'all', $type = 'all', $search = '', $page = 1, $perPage = 10, $sortBy = 'date_demande', $sortOrder = 'DESC') {
        try {
            // Debug: Log the user ID
            error_log("Fetching demandes for user ID: $userId");

            // First, let's check if the user has any demandes at all
            $checkQuery = "SELECT COUNT(*) as count FROM demandes_conges WHERE user_id = ?";
            $checkStmt = $this->db->prepare($checkQuery);
            $checkStmt->execute([$userId]);
            $count = $checkStmt->fetch()['count'];

            error_log("User $userId has $count demandes in the database");

            // Count total records for pagination
            $countQuery = "
                SELECT COUNT(*) as total
                FROM demandes_conges d
                LEFT JOIN users resp ON d.responsable_id = resp.id
                LEFT JOIN users plan ON d.planificateur_id = plan.id
                WHERE d.user_id = ?
            ";

            $countParams = [$userId];

            // Base query for data
            $query = "
                SELECT
                    d.id,
                    d.user_id,
                    d.type,
                    d.date_debut,
                    d.date_fin,
                    d.statut,
                    d.date_demande,
                    d.date_decision,
                    d.demi_journee,
                    d.demi_type,
                    d.motif,
                    d.justificatif,
                    d.motif_rejet,
                    d.responsable_id,
                    d.planificateur_id,
                    d.date_approbation_responsable,
                    d.date_approbation_planificateur,
                    d.reference_demande,
                    DATEDIFF(d.date_fin, d.date_debut) + 1 as duree,
                    -- Determine who made the final decision for 'Validé par' field
                    CASE
                        WHEN d.statut = 'refusee' AND d.planificateur_id IS NOT NULL THEN plan.nom
                        WHEN d.statut = 'refusee' AND d.responsable_id IS NOT NULL THEN resp.nom
                        WHEN d.statut = 'approuvee' AND d.planificateur_id IS NOT NULL THEN plan.nom
                        WHEN d.statut = 'acceptee' AND d.responsable_id IS NOT NULL THEN resp.nom
                        ELSE resp.nom
                    END AS valideur_nom,
                    CASE
                        WHEN d.statut = 'refusee' AND d.planificateur_id IS NOT NULL THEN plan.prenom
                        WHEN d.statut = 'refusee' AND d.responsable_id IS NOT NULL THEN resp.prenom
                        WHEN d.statut = 'approuvee' AND d.planificateur_id IS NOT NULL THEN plan.prenom
                        WHEN d.statut = 'acceptee' AND d.responsable_id IS NOT NULL THEN resp.prenom
                        ELSE resp.prenom
                    END AS valideur_prenom
                FROM demandes_conges d
                LEFT JOIN users resp ON d.responsable_id = resp.id
                LEFT JOIN users plan ON d.planificateur_id = plan.id
                WHERE d.user_id = ?
            ";

            $params = [$userId];

            // Apply period filter
            if ($period !== 'all') {
                $daysToSubtract = (int)$period;
                if ($daysToSubtract > 0) {
                    $filterSql = " AND d.date_demande >= DATE_SUB(NOW(), INTERVAL ? DAY)";
                    $query .= $filterSql;
                    $countQuery .= $filterSql;
                    $params[] = $daysToSubtract;
                    $countParams[] = $daysToSubtract;
                }
            }

            // Apply status filter
            if ($status !== 'all') {
                $filterSql = " AND d.statut = ?";
                $query .= $filterSql;
                $countQuery .= $filterSql;

                // Convert status values from filter to database values
                $statusValue = '';
                switch ($status) {
                    case 'pending':
                        // For pending, we need to check both responsable and planificateur stages
                        $filterSql = " AND (d.statut = 'en_attente_responsable' OR d.statut = 'en_attente_planificateur')";
                        $query .= $filterSql;
                        $countQuery .= $filterSql;
                        $statusValue = null; // Don't add to params since we handled it above
                        break;
                    case 'approved':
                        $statusValue = 'approuvee';
                        break;
                    case 'rejected':
                        $statusValue = 'refusee';
                        break;
                    case 'cancelled':
                        $statusValue = 'annulee';
                        break;
                    default:
                        $statusValue = $status;
                }

                // Debug the status value
                error_log("Status filter value: '$statusValue'");

                // Only add to params if statusValue is not null (for pending case)
                if ($statusValue !== null) {
                    $params[] = $statusValue;
                    $countParams[] = $statusValue;
                }
            }

            // Apply type filter
            if ($type !== 'all') {
                $filterSql = " AND d.type = ?";
                $query .= $filterSql;
                $countQuery .= $filterSql;

                // Convert type values from filter to database values
                $typeValue = '';
                switch ($type) {
                    case 'cp':
                        $typeValue = 'payé'; // Using UTF-8 character
                        break;
                    case 'css':
                        $typeValue = 'sans solde';
                        break;
                    case 'cm':
                        $typeValue = 'maladie';
                        break;
                    case 'cf':
                        $typeValue = 'familial';
                        break;
                    default:
                        $typeValue = $type;
                }

                // Debug the type value
                error_log("Type filter value: '$typeValue'");

                $params[] = $typeValue;
                $countParams[] = $typeValue;
            }

            // Apply search if provided
            if (!empty($search)) {
                $searchTerm = '%' . $search . '%';
                $filterSql = " AND (d.type LIKE ? OR d.motif LIKE ? OR d.reference_demande LIKE ? OR DATE_FORMAT(d.date_debut, '%d/%m/%Y') LIKE ? OR DATE_FORMAT(d.date_fin, '%d/%m/%Y') LIKE ?)";
                $query .= $filterSql;
                $countQuery .= $filterSql;

                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;

                $countParams[] = $searchTerm;
                $countParams[] = $searchTerm;
                $countParams[] = $searchTerm;
                $countParams[] = $searchTerm;
                $countParams[] = $searchTerm;
            }

            // Validate and sanitize sort parameters
            $allowedSortFields = ['date_demande', 'date_debut', 'date_fin', 'type', 'statut'];
            $allowedSortOrders = ['ASC', 'DESC'];

            if (!in_array($sortBy, $allowedSortFields)) {
                $sortBy = 'date_demande';
            }

            if (!in_array(strtoupper($sortOrder), $allowedSortOrders)) {
                $sortOrder = 'DESC';
            }

            // Add sorting
            $query .= " ORDER BY d." . $sortBy . " " . $sortOrder;

            // Add pagination
            if ($page < 1) $page = 1;
            $offset = ($page - 1) * $perPage;
            $query .= " LIMIT " . intval($perPage) . " OFFSET " . intval($offset);

            // Debug: Log the final query and parameters
            error_log("Count Query: $countQuery");
            error_log("Count Params: " . json_encode($countParams));
            error_log("Main Query: $query");
            error_log("Main Params: " . json_encode($params));

            // Execute count query
            $countStmt = $this->db->prepare($countQuery);
            $countStmt->execute($countParams);
            $totalRecords = $countStmt->fetch()['total'];
            $totalPages = ceil($totalRecords / $perPage);

            // Debug: Log the count results
            error_log("Total records found: $totalRecords, Total pages: $totalPages");

            // Execute main query
            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            $demandes = $stmt->fetchAll();

            // Debug: Log the number of results
            error_log("Number of demandes fetched: " . count($demandes));

            // Return both the results and pagination info
            return [
                'demandes' => $demandes,
                'pagination' => [
                    'total' => $totalRecords,
                    'perPage' => $perPage,
                    'currentPage' => $page,
                    'totalPages' => $totalPages
                ]
            ];
        } catch (PDOException $e) {
            error_log('Database error in getDemandesForUserWithFilters: ' . $e->getMessage());
            return [
                'demandes' => [],
                'pagination' => [
                    'total' => 0,
                    'perPage' => $perPage,
                    'currentPage' => $page,
                    'totalPages' => 0
                ]
            ];
        }
    }

    // Get a specific demande by ID
    public function getDemandeById($id, $userId = null) {
        try {
            $query = "
                SELECT d.*,
                       resp.nom AS responsable_nom, resp.prenom AS responsable_prenom,
                       plan.nom AS planificateur_nom, plan.prenom AS planificateur_prenom,
                       e.nom AS employe_nom, e.prenom AS employe_prenom,
                       -- Determine who made the final decision for 'Validé par' field
                       CASE
                           WHEN d.statut = 'refusee' AND d.planificateur_id IS NOT NULL THEN plan.nom
                           WHEN d.statut = 'refusee' AND d.responsable_id IS NOT NULL THEN resp.nom
                           WHEN d.statut = 'approuvee' AND d.planificateur_id IS NOT NULL THEN plan.nom
                           WHEN d.statut = 'acceptee' AND d.responsable_id IS NOT NULL THEN resp.nom
                           ELSE resp.nom
                       END AS valideur_nom,
                       CASE
                           WHEN d.statut = 'refusee' AND d.planificateur_id IS NOT NULL THEN plan.prenom
                           WHEN d.statut = 'refusee' AND d.responsable_id IS NOT NULL THEN resp.prenom
                           WHEN d.statut = 'approuvee' AND d.planificateur_id IS NOT NULL THEN plan.prenom
                           WHEN d.statut = 'acceptee' AND d.responsable_id IS NOT NULL THEN resp.prenom
                           ELSE resp.prenom
                       END AS valideur_prenom
                FROM demandes_conges d
                LEFT JOIN users resp ON d.responsable_id = resp.id
                LEFT JOIN users plan ON d.planificateur_id = plan.id
                JOIN users e ON d.user_id = e.id
                WHERE d.id = ?
            ";

            $params = [$id];

            // If userId is provided, ensure the demande belongs to this user
            if ($userId !== null) {
                $query .= " AND d.user_id = ?";
                $params[] = $userId;
            }

            $stmt = $this->db->prepare($query);
            $stmt->execute($params);

            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error fetching demande by ID: " . $e->getMessage());
            return false;
        }
    }

    // Get a specific demande by ID for a responsable (team manager)
    public function getDemandeByIdForResponsable($id, $responsableId) {
        try {
            $query = "
                SELECT d.*, u.nom AS valideur_nom, u.prenom AS valideur_prenom,
                       e.nom AS employe_nom, e.prenom AS employe_prenom
                FROM demandes_conges d
                LEFT JOIN users u ON d.responsable_id = u.id
                JOIN users e ON d.user_id = e.id
                WHERE d.id = ? AND e.responsable_id = ?
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$id, $responsableId]);

            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error fetching demande by ID for responsable: " . $e->getMessage());
            return false;
        }
    }

    // Create a new demande
    public function createDemande($userId, $type, $dateDebut, $dateFin, $motif, $demiJournee = 0, $demiType = null, $justificatif = null) {
        try {
            // Start transaction
            $this->db->beginTransaction();

            // Get user's matricule_rh for reference generation
            $userStmt = $this->db->prepare("SELECT matricule_rh FROM users WHERE id = ?");
            $userStmt->execute([$userId]);
            $user = $userStmt->fetch();

            if (!$user) {
                $this->db->rollBack();
                return false;
            }

            // Generate reference: DDMM-matricule_rh
            $reference = date('dm') . '-' . $user['matricule_rh'];

            // Insert the demande with reference - start with responsable approval stage
            $stmt = $this->db->prepare("
                INSERT INTO demandes_conges (reference_demande, user_id, type, date_debut, date_fin, motif, statut, date_demande, demi_journee, demi_type, justificatif)
                VALUES (?, ?, ?, ?, ?, ?, 'en_attente_responsable', NOW(), ?, ?, ?)
            ");
            $result = $stmt->execute([$reference, $userId, $type, $dateDebut, $dateFin, $motif, $demiJournee, $demiType, $justificatif]);

            if ($result) {
                $demandeId = $this->db->lastInsertId();
                $this->db->commit();
                return $demandeId;
            } else {
                $this->db->rollBack();
                return false;
            }
        } catch (PDOException $e) {
            $this->db->rollBack();
            error_log("Error creating demande: " . $e->getMessage());
            return false;
        }
    }

    // Update an existing demande
    public function updateDemande($id, $userId, $type, $dateDebut, $dateFin, $motif, $demiJournee = 0, $demiType = null, $justificatif = null) {
        // Check if justificatif is provided
        if ($justificatif) {
            $stmt = $this->db->prepare("
                UPDATE demandes_conges
                SET type = ?, date_debut = ?, date_fin = ?, motif = ?, demi_journee = ?, demi_type = ?, justificatif = ?
                WHERE id = ? AND user_id = ? AND statut = 'en_attente_responsable'
            ");
            return $stmt->execute([$type, $dateDebut, $dateFin, $motif, $demiJournee, $demiType, $justificatif, $id, $userId]);
        } else {
            // Don't update justificatif if not provided (keep existing value)
            $stmt = $this->db->prepare("
                UPDATE demandes_conges
                SET type = ?, date_debut = ?, date_fin = ?, motif = ?, demi_journee = ?, demi_type = ?
                WHERE id = ? AND user_id = ? AND statut = 'en_attente_responsable'
            ");
            return $stmt->execute([$type, $dateDebut, $dateFin, $motif, $demiJournee, $demiType, $id, $userId]);
        }
    }

    /**
     * Cancel a demande
     *
     * @param int $id - The ID of the demande to cancel
     * @param int $userId - The ID of the user cancelling the demande
     * @return bool - Success or failure
     */
    public function cancelDemande($id, $userId) {
        try {
            error_log("Starting cancelDemande: ID=$id, User=$userId");

            // Get the demande to check if it's still pending or approved
            $stmt = $this->db->prepare("
                SELECT *
                FROM demandes_conges
                WHERE id = ? AND user_id = ? AND (statut = 'en_attente_responsable' OR statut = 'en_attente_planificateur' OR statut = 'approuvee')
            ");
            $stmt->execute([$id, $userId]);
            $demande = $stmt->fetch();

            if (!$demande) {
                error_log("Demande not found or not in a cancellable state: ID=$id, User=$userId");
                return false; // Demande not found or not in a cancellable state
            }

            error_log("Found demande to cancel: " . json_encode($demande));

            // Start a transaction
            error_log("Starting transaction for cancelDemande");
            $this->db->beginTransaction();

            try {
                // Update the demande status to 'annulee' (without accent)
                $stmt = $this->db->prepare("
                    UPDATE demandes_conges
                    SET statut = 'annulee',
                        date_decision = NOW()
                    WHERE id = ? AND user_id = ? AND (statut = 'en_attente_responsable' OR statut = 'en_attente_planificateur' OR statut = 'approuvee')
                ");

                $result = $stmt->execute([$id, $userId]);
                $rowCount = $stmt->rowCount();
                error_log("Update result: " . ($result ? 'true' : 'false') . ", Rows affected: $rowCount");

                if (!$result || $rowCount === 0) {
                    error_log("Failed to update demande status: ID=$id, User=$userId");
                    $this->db->rollBack();
                    return false;
                }

                // If the demande was approved, restore the leave balance
                if ($demande['statut'] === 'approuvee') {
                    // Calculate the number of days for this request
                    $demiJournee = $demande['demi_journee'] ? true : false;
                    $demiType = $demande['demi_type'];
                    $requestDays = $this->calculateLeaveDays($demande['date_debut'], $demande['date_fin'], $demiJournee, $demiType);

                    error_log("Calculated days for leave request: $requestDays days (demiJournee: " . ($demiJournee ? 'true' : 'false') . ", demiType: " . ($demiType ?: 'none') . ")");
                    error_log("Date range: {$demande['date_debut']} to {$demande['date_fin']}");

                    // Restore the leave balance
                    $leaveBalanceModel = new LeaveBalanceModel();
                    $balanceRestored = $leaveBalanceModel->restoreLeaveBalance(
                        $userId,
                        $demande['type'],
                        $requestDays
                    );

                    if (!$balanceRestored) {
                        error_log("Failed to restore leave balance: User=$userId, Type={$demande['type']}, Days=$requestDays");
                        $this->db->rollBack();
                        return false;
                    }

                    // Restore the department leave balance
                    $userModel = new UserModel();
                    $user = $userModel->getUserById($userId);
                    $departmentName = $user['departement'];

                    $departmentLeaveBalanceModel = new DepartmentLeaveBalanceModel();
                    $departmentBalanceRestored = $departmentLeaveBalanceModel->restoreDepartmentLeaveBalance(
                        $departmentName,
                        $requestDays,
                        $demande['type']
                    );

                    if (!$departmentBalanceRestored) {
                        error_log("Failed to restore department leave balance: Department=$departmentName, Type={$demande['type']}, Days=$requestDays");
                        // Don't roll back for department balance failures - just log
                    }

                    error_log("Leave balance restored successfully");
                }

                // Note: No notification is sent when user cancels their own request
                // Notifications are only sent when managers approve or refuse requests

                // Log the cancellation
                error_log('Demande #' . $id . ' cancelled by user #' . $userId);

                // Commit the transaction
                error_log("Committing transaction for cancelDemande");
                $this->db->commit();
                error_log("Transaction committed successfully");
                return true;
            } catch (Exception $e) {
                $this->db->rollBack();
                error_log('Transaction error in cancelDemande: ' . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString());
                return false;
            }
        } catch (PDOException $e) {
            error_log('Database error in cancelDemande: ' . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString());
            return false;
        } catch (Exception $e) {
            error_log('General error in cancelDemande: ' . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Get pending demandes for approval by manager
     *
     * @param int $responsableId - Optional ID of the responsable to filter by
     * @return array - List of pending demandes
     */
    public function getPendingDemandesForApproval($responsableId = null) {
        try {
            if ($responsableId) {
                // Get the responsable
                $userModel = new UserModel();
                $responsable = $userModel->getUserById($responsableId);

                if (!$responsable || $responsable['role'] !== 'responsable') {
                    return [];
                }

                // Get all pending demandes from users managed by this responsable
                $query = "
                    SELECT
                        d.*,
                        u.nom,
                        u.prenom,
                        u.departement,
                        DATEDIFF(d.date_fin, d.date_debut) + 1 as nbJours
                    FROM demandes_conges d
                    JOIN users u ON d.user_id = u.id
                    WHERE d.statut = 'en_attente_responsable'
                    AND u.manager_id = ?
                    ORDER BY d.date_demande ASC
                ";

                $stmt = $this->db->prepare($query);
                $stmt->execute([$responsableId]);
            } else {
                // Get all pending demandes
                $query = "
                    SELECT
                        d.*,
                        u.nom,
                        u.prenom,
                        u.departement,
                        DATEDIFF(d.date_fin, d.date_debut) + 1 as nbJours
                    FROM demandes_conges d
                    JOIN users u ON d.user_id = u.id
                    WHERE (d.statut = 'en_attente_responsable' OR d.statut = 'en_attente_planificateur')
                    ORDER BY d.date_demande ASC
                ";

                $stmt = $this->db->prepare($query);
                $stmt->execute();
            }

            $demandes = $stmt->fetchAll();

            // Format the type values
            foreach ($demandes as &$demande) {
                $demande['type'] = $this->formatLeaveType($demande['type']);
            }

            return $demandes;
        } catch (PDOException $e) {
            error_log('Database error in getPendingDemandesForApproval: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get pending demandes for approval with advanced filtering options
     *
     * @param int $responsableId - The ID of the responsable
     * @param string $employeeFilter - Filter by specific employee ID or 'all'
     * @param string $typeFilter - Filter by leave type or 'all'
     * @param string $dateFilter - Filter by date range ('recent', 'week', 'month', 'all')
     * @return array - List of pending demandes
     */
    public function getPendingDemandesForApprovalWithFilters($responsableId, $employeeFilter = 'all', $typeFilter = 'all', $dateFilter = 'all') {
        try {
            // Start building the query
            $query = "
                SELECT
                    d.*,
                    u.nom,
                    u.prenom,
                    u.departement,
                    DATEDIFF(d.date_fin, d.date_debut) + 1 as nbJours
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE d.statut = 'en_attente_responsable'
                AND u.manager_id = ?
            ";
            $params = [$responsableId];

            // Add employee filter
            if ($employeeFilter !== 'all') {
                $query .= " AND d.user_id = ?";
                $params[] = $employeeFilter;
            }

            // Add type filter
            if ($typeFilter !== 'all') {
                $query .= " AND d.type = ?";
                $params[] = $typeFilter;
            }

            // Add date filter
            switch ($dateFilter) {
                case 'recent':
                    $query .= " AND d.date_demande >= DATE_SUB(NOW(), INTERVAL 2 DAY)";
                    break;
                case 'week':
                    $query .= " AND d.date_demande >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
                    break;
                case 'month':
                    $query .= " AND d.date_demande >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
                    break;
                // 'all' doesn't need additional filters
            }

            // Add order by
            $query .= " ORDER BY d.date_demande ASC";

            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            $demandes = $stmt->fetchAll();

            // Format the type values
            foreach ($demandes as &$demande) {
                $demande['type'] = $this->formatLeaveType($demande['type']);
            }

            return $demandes;
        } catch (PDOException $e) {
            error_log('Database error in getPendingDemandesForApprovalWithFilters: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Approve a demande by responsable (first step of two-step approval)
     *
     * @param int $id - The ID of the demande to approve
     * @param int $responsableId - The ID of the responsable approving the demande
     * @return bool - Success or failure
     */
    public function approveDemandeByResponsable($id, $responsableId) {
        try {
            // Get the demande to check if it's waiting for responsable approval
            $stmt = $this->db->prepare("
                SELECT d.*, u.departement as user_dept, u.nom as user_nom, u.prenom as user_prenom, u.manager_id
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE d.id = ? AND d.statut = 'en_attente_responsable'
            ");
            $stmt->execute([$id]);
            $demande = $stmt->fetch();

            if (!$demande) {
                error_log("Demande not found or not waiting for responsable approval: ID=$id, Responsable=$responsableId");
                return false;
            }

            // Check if the responsable is trying to approve their own request
            if ($demande['user_id'] == $responsableId) {
                error_log("Responsable cannot approve their own request: ID=$id, Responsable=$responsableId");
                return false;
            }

            // Check if the responsable is authorized (must be the manager of the employee)
            if ($demande['manager_id'] != $responsableId) {
                error_log("Responsable not authorized to approve this demande: ID=$id, Responsable=$responsableId, Required Manager={$demande['manager_id']}");
                return false;
            }

            // Start a transaction
            $this->db->beginTransaction();

            try {
                // Update the demande to move to planificateur approval stage
                $stmt = $this->db->prepare("
                    UPDATE demandes_conges
                    SET statut = 'en_attente_planificateur',
                        responsable_id = ?,
                        date_approbation_responsable = NOW()
                    WHERE id = ? AND statut = 'en_attente_responsable'
                ");

                $result = $stmt->execute([$responsableId, $id]);

                // Check if any rows were affected
                if (!$result || $stmt->rowCount() === 0) {
                    error_log("Failed to update demande status or no rows affected: ID=$id, Responsable=$responsableId");
                    $this->db->rollBack();
                    return false;
                }

                // Log the responsable approval
                error_log('Demande #' . $id . ' approved by responsable #' . $responsableId . ' for user ' . $demande['user_prenom'] . ' ' . $demande['user_nom'] . '. Moving to planificateur approval stage.');

                // Commit the transaction
                $this->db->commit();
                return true;
            } catch (Exception $e) {
                $this->db->rollBack();
                error_log('Transaction error in approveDemandeByResponsable: ' . $e->getMessage());
                return false;
            }
        } catch (PDOException $e) {
            error_log('Database error in approveDemandeByResponsable: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Approve a demande by planificateur (final step of two-step approval)
     *
     * @param int $id - The ID of the demande to approve
     * @param int $planificateurId - The ID of the planificateur approving the demande
     * @return bool - Success or failure
     */
    public function approveDemandeByPlanificateur($id, $planificateurId) {
        try {
            // Get the demande to check if it's waiting for planificateur approval
            $stmt = $this->db->prepare("
                SELECT d.*, u.departement as user_dept, u.nom as user_nom, u.prenom as user_prenom
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE d.id = ? AND d.statut = 'en_attente_planificateur'
            ");
            $stmt->execute([$id]);
            $demande = $stmt->fetch();

            if (!$demande) {
                error_log("Demande not found or not waiting for planificateur approval: ID=$id, Planificateur=$planificateurId");
                return false;
            }

            // Check if the planificateur is trying to approve their own request
            if ($demande['user_id'] == $planificateurId) {
                error_log("Planificateur cannot approve their own request: ID=$id, Planificateur=$planificateurId");
                return false;
            }

            // Calculate the number of days for this request
            $demiJournee = $demande['demi_journee'] ? true : false;
            $demiType = $demande['demi_type'];
            $requestDays = $this->calculateLeaveDays($demande['date_debut'], $demande['date_fin'], $demiJournee, $demiType);

            // Check if the user has sufficient leave balance
            $leaveBalanceModel = new LeaveBalanceModel();
            if (!$leaveBalanceModel->hasSufficientBalance($demande['user_id'], $demande['type'], $requestDays)) {
                error_log('Insufficient leave balance for demande #' . $id . ' by user #' . $demande['user_id']);
                return false;
            }

            // Start a transaction
            $this->db->beginTransaction();

            try {
                // Update the demande to final approved status
                $stmt = $this->db->prepare("
                    UPDATE demandes_conges
                    SET statut = 'approuvee',
                        planificateur_id = ?,
                        date_approbation_planificateur = NOW(),
                        date_decision = NOW()
                    WHERE id = ? AND statut = 'en_attente_planificateur'
                ");

                $result = $stmt->execute([$planificateurId, $id]);

                // Check if any rows were affected
                if (!$result || $stmt->rowCount() === 0) {
                    error_log("Failed to update demande status or no rows affected: ID=$id, Planificateur=$planificateurId");
                    $this->db->rollBack();
                    return false;
                }

                // Deduct from leave balance
                $leaveBalanceModel = new LeaveBalanceModel();
                $balanceUpdated = $leaveBalanceModel->deductLeaveBalance(
                    $demande['user_id'],
                    $demande['type'],
                    $requestDays
                );

                if (!$balanceUpdated) {
                    $this->db->rollBack();
                    return false;
                }

                // Deduct from department leave balance
                $departmentLeaveBalanceModel = new DepartmentLeaveBalanceModel();
                $deptBalanceUpdated = $departmentLeaveBalanceModel->deductDepartmentLeaveBalance(
                    $demande['user_dept'],
                    $requestDays,
                    $demande['type']
                );

                if (!$deptBalanceUpdated) {
                    $this->db->rollBack();
                    return false;
                }

                // Calculate the number of days
                $dateDebut = new DateTime($demande['date_debut']);
                $dateFin = new DateTime($demande['date_fin']);
                $interval = $dateDebut->diff($dateFin);
                $nbJours = $interval->days + 1;

                // Format dates for notification
                $dateDebutFormatted = $dateDebut->format('d/m/Y');
                $dateFinFormatted = $dateFin->format('d/m/Y');

                // Create a notification for the employee (only at final approval)
                $notificationModel = new NotificationModel();
                $referenceText = !empty($demande['reference_demande']) ? '[' . $demande['reference_demande'] . '] ' : '';
                $notificationModel->createNotification(
                    $demande['user_id'],
                    'success',
                    'Demande approuvée',
                    'Votre demande de congé ' . $referenceText . 'du ' . $dateDebutFormatted . ' au ' . $dateFinFormatted . ' (' . $nbJours . ' jour(s)) a été définitivement approuvée.',
                    $id
                );

                // Log the final approval
                error_log('Demande #' . $id . ' finally approved by planificateur #' . $planificateurId . ' for user ' . $demande['user_prenom'] . ' ' . $demande['user_nom']);

                // Commit the transaction
                $this->db->commit();
                return true;
            } catch (Exception $e) {
                $this->db->rollBack();
                error_log('Transaction error in approveDemandeByPlanificateur: ' . $e->getMessage());
                return false;
            }
        } catch (PDOException $e) {
            error_log('Database error in approveDemandeByPlanificateur: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Legacy method for backward compatibility - determines approval type based on user role
     *
     * @param int $id - The ID of the demande to approve
     * @param int $approverId - The ID of the user approving the demande
     * @return bool - Success or failure
     */
    public function approveDemande($id, $approverId) {
        // Get the approver's role
        $userModel = new UserModel();
        $approver = $userModel->getUserById($approverId);

        if (!$approver) {
            return false;
        }

        // Route to appropriate approval method based on role
        if ($approver['role'] === 'responsable') {
            return $this->approveDemandeByResponsable($id, $approverId);
        } elseif ($approver['role'] === 'planificateur') {
            return $this->approveDemandeByPlanificateur($id, $approverId);
        }

        return false;
    }

    /**
     * Reject a demande
     *
     * @param int $id - The ID of the demande to reject
     * @param int $approverId - The ID of the user rejecting the demande
     * @param string $motifRejet - The reason for rejection
     * @return bool - Success or failure
     */
    public function rejectDemande($id, $approverId, $motifRejet) {
        try {
            error_log("Starting rejectDemande: ID=$id, Approver=$approverId, Motif=$motifRejet");

            // Get the demande to check if it's still pending (either stage)
            $stmt = $this->db->prepare("
                SELECT d.*, u.departement as user_dept, u.nom as user_nom, u.prenom as user_prenom, u.manager_id
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE d.id = ? AND (d.statut = 'en_attente_responsable' OR d.statut = 'en_attente_planificateur')
            ");
            $stmt->execute([$id]);
            $demande = $stmt->fetch();

            if (!$demande) {
                error_log("Demande not found or not pending: ID=$id");
                return false; // Demande not found or not pending
            }

            error_log("Found demande: " . json_encode($demande));

            // Check if the approver is trying to reject their own request
            if ($demande['user_id'] == $approverId) {
                error_log("Approver cannot reject their own request: ID=$id, Approver=$approverId");
                return false; // Cannot reject own request
            }

            // Check if the approver is authorized based on current status and role
            $userModel = new UserModel();
            $approver = $userModel->getUserById($approverId);

            if (!$approver) {
                error_log("Approver not found: ID=$approverId");
                return false;
            }

            // Authorization logic based on current status
            if ($demande['statut'] === 'en_attente_responsable') {
                // Only the assigned responsable can reject at this stage
                if ($approver['role'] !== 'responsable' || $approverId != $demande['manager_id']) {
                    error_log("Unauthorized responsable rejection: Approver=$approverId, Required Manager={$demande['manager_id']}");
                    return false;
                }
            } elseif ($demande['statut'] === 'en_attente_planificateur') {
                // Only planificateurs can reject at this stage
                if ($approver['role'] !== 'planificateur') {
                    error_log("Unauthorized planificateur rejection: Approver=$approverId, Role={$approver['role']}");
                    return false;
                }
            }

            // Start a transaction
            error_log("Starting transaction for rejectDemande");
            $this->db->beginTransaction();

            try {
                // Check if the demande was previously approved and needs to have the balance restored
                $stmt = $this->db->prepare("
                    SELECT statut
                    FROM demandes_conges
                    WHERE id = ?
                ");
                $stmt->execute([$id]);
                $previousStatus = $stmt->fetchColumn();
                error_log("Previous status: $previousStatus");

                // Update the demande based on current status
                if ($demande['statut'] === 'en_attente_responsable') {
                    $updateQuery = "
                        UPDATE demandes_conges
                        SET statut = 'refusee',
                            responsable_id = ?,
                            motif_rejet = ?,
                            date_decision = NOW()
                        WHERE id = ? AND statut = 'en_attente_responsable'
                    ";
                } else { // en_attente_planificateur
                    $updateQuery = "
                        UPDATE demandes_conges
                        SET statut = 'refusee',
                            planificateur_id = ?,
                            motif_rejet = ?,
                            date_decision = NOW()
                        WHERE id = ? AND statut = 'en_attente_planificateur'
                    ";
                }

                error_log("Update query: $updateQuery");
                error_log("Parameters: Approver=$approverId, Motif=$motifRejet, ID=$id");

                $stmt = $this->db->prepare($updateQuery);
                $result = $stmt->execute([$approverId, $motifRejet, $id]);

                // Check if any rows were affected
                $rowCount = $stmt->rowCount();
                error_log("Update result: " . ($result ? 'true' : 'false') . ", Rows affected: $rowCount");

                if (!$result || $rowCount === 0) {
                    error_log("Failed to update demande status or no rows affected: ID=$id, Approver=$approverId");
                    $this->db->rollBack();
                    return false;
                }

                // If the demande was previously approved, restore the leave balance
                if ($previousStatus === 'approuvee') {
                    // Calculate the number of days for this request
                    $demiJournee = $demande['demi_journee'] ? true : false;
                    $demiType = $demande['demi_type'];
                    $requestDays = $this->calculateLeaveDays($demande['date_debut'], $demande['date_fin'], $demiJournee, $demiType);
                    $demiType = $demande['demi_type'];
                    $requestDays = $this->calculateLeaveDays($demande['date_debut'], $demande['date_fin'], $demiJournee, $demiType);

                    // Restore the leave balance
                    $leaveBalanceModel = new LeaveBalanceModel();
                    $balanceRestored = $leaveBalanceModel->restoreLeaveBalance(
                        $demande['user_id'],
                        $demande['type'],
                        $requestDays
                    );

                    if (!$balanceRestored) {
                        $this->db->rollBack();
                        return false;
                    }

                    // Restore the department leave balance
                    $userModel = new UserModel();
                    $user = $userModel->getUserById($demande['user_id']);
                    $departmentName = $user['departement'];

                    $departmentLeaveBalanceModel = new DepartmentLeaveBalanceModel();
                    $departmentBalanceRestored = $departmentLeaveBalanceModel->restoreDepartmentLeaveBalance(
                        $departmentName,
                        $requestDays,
                        $demande['type']
                    );

                    if (!$departmentBalanceRestored) {
                        error_log("Failed to restore department leave balance: Department=$departmentName, Type={$demande['type']}, Days=$requestDays");
                        // Don't roll back for department balance failures - just log
                    }
                }

                // Calculate the number of days
                $dateDebut = new DateTime($demande['date_debut']);
                $dateFin = new DateTime($demande['date_fin']);
                $interval = $dateDebut->diff($dateFin);
                $nbJours = $interval->days + 1; // +1 because the end date is inclusive

                // Format dates for notification
                $dateDebutFormatted = $dateDebut->format('d/m/Y');
                $dateFinFormatted = $dateFin->format('d/m/Y');

                // Create a notification for the employee
                $notificationModel = new NotificationModel();
                $referenceText = !empty($demande['reference_demande']) ? '[' . $demande['reference_demande'] . '] ' : '';
                $notificationModel->createNotification(
                    $demande['user_id'],
                    'error',
                    'Demande refusée',
                    'Votre demande de congé ' . $referenceText . 'du ' . $dateDebutFormatted . ' au ' . $dateFinFormatted . ' (' . $nbJours . ' jour(s)) a été refusée. Motif: ' . $motifRejet,
                    $id // Include demande_id for clickable navigation
                );

                // Log the rejection
                error_log('Demande #' . $id . ' rejected by responsable #' . $approverId . ' for user ' . $demande['user_prenom'] . ' ' . $demande['user_nom'] . '. Motif: ' . $motifRejet);

                // Commit the transaction
                error_log("Committing transaction for rejectDemande");
                $this->db->commit();
                error_log("Transaction committed successfully");
                return true;
            } catch (Exception $e) {
                error_log('Transaction error in rejectDemande: ' . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString());
                $this->db->rollBack();
                return false;
            }
        } catch (PDOException $e) {
            error_log('Database error in rejectDemande: ' . $e->getMessage());
            return false;
        } catch (Exception $e) {
            error_log('General error in rejectDemande: ' . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString());
            return false;
        }
    }



    /**
     * Get the history of processed demandes for a responsable
     * Enhanced to include rejection details for complete information
     *
     * @param int $responsableId - The ID of the responsable
     * @param string $period - Optional period filter (30, 90, 180, 365 days)
     * @param string $status - Optional status filter (approved, rejected)
     * @param int $employeeId - Optional employee filter
     * @return array - List of processed demandes with rejection details
     */
    public function getDemandesHistoryForResponsable($responsableId, $period = null, $status = null, $employeeId = null) {
        try {
            // Get the responsable
            $userModel = new UserModel();
            $responsable = $userModel->getUserById($responsableId);

            if (!$responsable || $responsable['role'] !== 'responsable') {
                return [];
            }

            // Build the query with rejection details
            $query = "
                SELECT
                    d.*,
                    u.nom,
                    u.prenom,
                    u.departement,
                    DATEDIFF(d.date_fin, d.date_debut) + 1 as nbJours,
                    COALESCE(d.date_decision, d.date_demande) as date_decision,
                    -- Rejection details
                    resp_rejector.nom as responsable_rejector_nom,
                    resp_rejector.prenom as responsable_rejector_prenom,
                    plan_rejector.nom as planificateur_rejector_nom,
                    plan_rejector.prenom as planificateur_rejector_prenom
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                LEFT JOIN users resp_rejector ON d.responsable_id = resp_rejector.id
                LEFT JOIN users plan_rejector ON d.planificateur_id = plan_rejector.id
                WHERE u.manager_id = ?
                AND d.statut NOT IN ('en_attente_responsable', 'en_attente_planificateur')
            ";

            $params = [$responsableId];

            // Add period filter if provided
            if ($period) {
                $query .= " AND d.date_demande >= DATE_SUB(NOW(), INTERVAL ? DAY)";
                $params[] = intval($period);
            }

            // Add status filter if provided
            if ($status) {
                if ($status === 'approved') {
                    $query .= " AND d.statut = 'acceptee'";
                } elseif ($status === 'rejected') {
                    $query .= " AND d.statut = 'refusee'";
                }
            }

            // Add employee filter if provided
            if ($employeeId) {
                $query .= " AND d.user_id = ?";
                $params[] = intval($employeeId);
            }

            $query .= " ORDER BY d.date_demande DESC";

            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            $demandes = $stmt->fetchAll();

            // Format the data for display
            foreach ($demandes as &$demande) {
                $demande['type'] = $this->formatLeaveType($demande['type']);

                // Format status and add rejection details
                switch ($demande['statut']) {
                    case 'acceptee':
                    case 'approuvee':
                        $demande['statut'] = 'Approuvée';
                        break;
                    case 'refusee':
                        $demande['statut'] = 'Rejetée';

                        // Add rejection details for rejected requests
                        $demande['rejection_details'] = $this->formatRejectionDetails($demande);
                        break;
                    case 'annulee':
                        $demande['statut'] = 'Annulée';
                        break;
                }

                // Format dates for display
                if (!empty($demande['date_decision'])) {
                    $demande['date_decision_formatted'] = date('d/m/Y à H:i', strtotime($demande['date_decision']));
                } else {
                    $demande['date_decision_formatted'] = 'Non disponible';
                }
            }

            return $demandes;
        } catch (PDOException $e) {
            error_log('Database error in getDemandesHistoryForResponsable: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all pending demandes for planificateur approval
     * Now includes both responsable and planificateur pending stages
     *
     * @return array - List of demandes in pending approval stages
     */
    public function getAllPendingDemandes() {
        try {
            // Get demandes waiting for either responsable or planificateur approval
            $query = "
                SELECT d.*, u.nom, u.prenom, u.departement, u.manager_id,
                       resp.nom as responsable_nom, resp.prenom as responsable_prenom
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                LEFT JOIN users resp ON u.manager_id = resp.id
                WHERE d.statut IN ('en_attente_responsable', 'en_attente_planificateur')
                ORDER BY
                    CASE
                        WHEN d.statut = 'en_attente_planificateur' THEN 1
                        WHEN d.statut = 'en_attente_responsable' THEN 2
                    END,
                    d.date_demande DESC
            ";
            $stmt = $this->db->prepare($query);
            $stmt->execute();

            $demandes = $stmt->fetchAll();

            // Format the demandes
            foreach ($demandes as &$demande) {
                // Calculate the number of days
                $dateDebut = new DateTime($demande['date_debut']);
                $dateFin = new DateTime($demande['date_fin']);
                $interval = $dateDebut->diff($dateFin);
                $demande['nbJours'] = $interval->days + 1; // +1 because the end date is inclusive

                // Format the dates
                $demande['date_debut_formatted'] = $dateDebut->format('d/m/Y');
                $demande['date_fin_formatted'] = $dateFin->format('d/m/Y');

                // Format the type
                $demande['type_formatted'] = $this->formatLeaveType($demande['type']);

                // Add status information for UI display
                $demande['is_actionable_by_planificateur'] = ($demande['statut'] === 'en_attente_planificateur');
                $demande['status_display'] = $this->getStatusDisplayText($demande['statut']);
                $demande['status_color'] = $this->getStatusColor($demande['statut']);

                // Add responsable information
                if (!empty($demande['responsable_nom']) && !empty($demande['responsable_prenom'])) {
                    $demande['responsable_full_name'] = $demande['responsable_prenom'] . ' ' . $demande['responsable_nom'];
                } else {
                    $demande['responsable_full_name'] = 'Non assigné';
                }
            }

            return $demandes;
        } catch (PDOException $e) {
            error_log('Database error in getAllPendingDemandes: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get display text for status
     *
     * @param string $status - The status value
     * @return string - Human readable status text
     */
    private function getStatusDisplayText($status) {
        switch ($status) {
            case 'en_attente_responsable':
                return 'En attente du responsable';
            case 'en_attente_planificateur':
                return 'En attente du planificateur';
            case 'approuvee':
                return 'Approuvée';
            case 'refusee':
                return 'Refusée';
            case 'annulee':
                return 'Annulée';
            default:
                return 'Statut inconnu';
        }
    }

    /**
     * Get color class for status
     *
     * @param string $status - The status value
     * @return string - CSS color class
     */
    private function getStatusColor($status) {
        switch ($status) {
            case 'en_attente_responsable':
                return 'yellow'; // Waiting for manager
            case 'en_attente_planificateur':
                return 'blue'; // Ready for planner action
            case 'approuvee':
                return 'green';
            case 'refusee':
                return 'red';
            case 'annulee':
                return 'gray';
            default:
                return 'gray';
        }
    }

    /**
     * Format rejection details for display
     *
     * @param array $demande - The demande data with rejection information
     * @return array - Formatted rejection details
     */
    private function formatRejectionDetails($demande) {
        $rejectionDetails = [
            'rejected_by_name' => null,
            'rejected_by_role' => null,
            'rejection_reason' => null,
            'rejection_date' => null
        ];

        // Determine who rejected the request and their role
        if (!empty($demande['responsable_rejector_nom']) && !empty($demande['responsable_rejector_prenom'])) {
            $rejectionDetails['rejected_by_name'] = $demande['responsable_rejector_prenom'] . ' ' . $demande['responsable_rejector_nom'];
            $rejectionDetails['rejected_by_role'] = 'Responsable';
        } elseif (!empty($demande['planificateur_rejector_nom']) && !empty($demande['planificateur_rejector_prenom'])) {
            $rejectionDetails['rejected_by_name'] = $demande['planificateur_rejector_prenom'] . ' ' . $demande['planificateur_rejector_nom'];
            $rejectionDetails['rejected_by_role'] = 'Planificateur';
        } else {
            $rejectionDetails['rejected_by_name'] = 'Utilisateur inconnu';
            $rejectionDetails['rejected_by_role'] = 'Rôle inconnu';
        }

        // Add rejection reason
        $rejectionDetails['rejection_reason'] = !empty($demande['motif_rejet'])
            ? $demande['motif_rejet']
            : 'Aucun motif spécifié';

        // Add rejection date
        $rejectionDetails['rejection_date'] = !empty($demande['date_decision'])
            ? date('d/m/Y à H:i', strtotime($demande['date_decision']))
            : 'Date inconnue';

        return $rejectionDetails;
    }

    // Get statistics for a user
    public function getUserStatistics($userId) {
        // Total demandes
        $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM demandes_conges WHERE user_id = ?");
        $stmt->execute([$userId]);
        $totalDemandes = $stmt->fetch()['total'];

        // Approved demandes
        $stmt = $this->db->prepare("SELECT COUNT(*) as approved FROM demandes_conges WHERE user_id = ? AND statut = 'approuvee'");
        $stmt->execute([$userId]);
        $approvedDemandes = $stmt->fetch()['approved'];

        // Rejected demandes
        $stmt = $this->db->prepare("SELECT COUNT(*) as rejected FROM demandes_conges WHERE user_id = ? AND statut = 'refusee'");
        $stmt->execute([$userId]);
        $rejectedDemandes = $stmt->fetch()['rejected'];

        // Pending demandes (both stages)
        $stmt = $this->db->prepare("SELECT COUNT(*) as pending FROM demandes_conges WHERE user_id = ? AND (statut = 'en_attente_responsable' OR statut = 'en_attente_planificateur')");
        $stmt->execute([$userId]);
        $pendingDemandes = $stmt->fetch()['pending'];

        return [
            'total' => $totalDemandes,
            'approved' => $approvedDemandes,
            'rejected' => $rejectedDemandes,
            'pending' => $pendingDemandes,
            'approval_rate' => $totalDemandes > 0 ? round(($approvedDemandes / $totalDemandes) * 100) : 0
        ];
    }



    // Get monthly statistics for a user
    public function getMonthlyStatistics($userId, $year = null) {
        if ($year === null) {
            $year = date('Y');
        }

        $monthlyData = [
            'Jan' => 0, 'Feb' => 0, 'Mar' => 0, 'Apr' => 0,
            'May' => 0, 'Jun' => 0, 'Jul' => 0, 'Aug' => 0,
            'Sep' => 0, 'Oct' => 0, 'Nov' => 0, 'Dec' => 0
        ];

        $stmt = $this->db->prepare("
            SELECT
                MONTH(date_debut) as month,
                SUM(DATEDIFF(date_fin, date_debut) + 1) as days
            FROM demandes_conges
            WHERE user_id = ?
            AND YEAR(date_debut) = ?
            AND (statut = 'approuvee' OR statut = 'en_attente_responsable' OR statut = 'en_attente_planificateur')
            GROUP BY MONTH(date_debut)
        ");
        $stmt->execute([$userId, $year]);
        $results = $stmt->fetchAll();

        foreach ($results as $row) {
            $monthIndex = $row['month'] - 1; // Convert 1-12 to 0-11
            $monthName = date('M', mktime(0, 0, 0, $row['month'], 1));
            $monthlyData[$monthName] = (int)$row['days'];
        }

        return $monthlyData;
    }

    // Get leave type distribution for a user
    public function getLeaveTypeDistribution($userId) {
        $stmt = $this->db->prepare("
            SELECT
                type,
                COUNT(*) as count
            FROM demandes_conges
            WHERE user_id = ?
            GROUP BY type
        ");
        $stmt->execute([$userId]);
        $results = $stmt->fetchAll();

        $distribution = [];
        foreach ($results as $row) {
            $distribution[$row['type']] = (int)$row['count'];
        }

        return $distribution;
    }

    // Get upcoming leaves for a user
    public function getUpcomingLeaves($userId, $limit = 3) {
        $stmt = $this->db->prepare("
            SELECT
                id, type, date_debut, date_fin, reference_demande,
                DATEDIFF(date_fin, date_debut) + 1 as duree
            FROM demandes_conges
            WHERE user_id = ?
            AND date_debut >= CURDATE()
            AND (statut = 'approuvee' OR statut = 'en_attente_responsable' OR statut = 'en_attente_planificateur')
            ORDER BY date_debut ASC
            LIMIT " . intval($limit) . "
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }

    // Get recent leave history for a user
    public function getRecentHistory($userId, $limit = 5) {
        $stmt = $this->db->prepare("
            SELECT
                id, type, date_debut, date_fin, statut, date_demande, reference_demande,
                DATEDIFF(date_fin, date_debut) + 1 as duree
            FROM demandes_conges
            WHERE user_id = ?
            ORDER BY date_demande DESC
            LIMIT " . intval($limit) . "
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }

    // Get global statistics (for admin)
    public function getGlobalStatistics() {
        // Total demandes
        $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM demandes_conges");
        $stmt->execute();
        $totalDemandes = $stmt->fetch()['total'];            // Approved demandes
            $stmt = $this->db->prepare("SELECT COUNT(*) as approved FROM demandes_conges WHERE statut = 'approuvee'");
            $stmt->execute();
            $approvedDemandes = $stmt->fetch()['approved'];

            // Rejected demandes
            $stmt = $this->db->prepare("SELECT COUNT(*) as rejected FROM demandes_conges WHERE statut = 'refusee'");
        $stmt->execute();
        $rejectedDemandes = $stmt->fetch()['rejected'];

        // Pending demandes
        $stmt = $this->db->prepare("SELECT COUNT(*) as pending FROM demandes_conges WHERE (statut = 'en_attente_responsable' OR statut = 'en_attente_planificateur')");
        $stmt->execute();
        $pendingDemandes = $stmt->fetch()['pending'];

        return [
            'total' => $totalDemandes,
            'approved' => $approvedDemandes,
            'rejected' => $rejectedDemandes,
            'pending' => $pendingDemandes,
            'approval_rate' => $totalDemandes > 0 ? round(($approvedDemandes / $totalDemandes) * 100) : 0
        ];
    }

    // Get team statistics (for responsable)
    public function getTeamStatistics($managerId) {
        // For now, we'll just get global stats
        // In a real app, you would filter by team members
        return $this->getGlobalStatistics();
    }

    // Get monthly statistics for all users
    public function getMonthlyStatsForAllUsers($year = null) {
        if ($year === null) {
            $year = date('Y');
        }

        $monthlyData = [
            'Jan' => 0, 'Feb' => 0, 'Mar' => 0, 'Apr' => 0,
            'May' => 0, 'Jun' => 0, 'Jul' => 0, 'Aug' => 0,
            'Sep' => 0, 'Oct' => 0, 'Nov' => 0, 'Dec' => 0
        ];

        $stmt = $this->db->prepare("
            SELECT
                MONTH(date_debut) as month,
                SUM(DATEDIFF(date_fin, date_debut) + 1) as days
            FROM demandes_conges
            WHERE YEAR(date_debut) = ?
            GROUP BY MONTH(date_debut)
        ");
        $stmt->execute([$year]);
        $results = $stmt->fetchAll();

        foreach ($results as $row) {
            $monthName = date('M', mktime(0, 0, 0, $row['month'], 1));
            $monthlyData[$monthName] = (int)$row['days'];
        }

        return $monthlyData;
    }

    // Get leave type distribution for all users
    public function getTypeDistributionForAllUsers() {
        $stmt = $this->db->prepare("
            SELECT
                type,
                COUNT(*) as count
            FROM demandes_conges
            GROUP BY type
        ");
        $stmt->execute();
        $results = $stmt->fetchAll();

        $distribution = [];
        foreach ($results as $row) {
            $typeName = $this->formatLeaveType($row['type']);
            $distribution[$typeName] = (int)$row['count'];
        }

        return $distribution;
    }

    /**
     * Get department absence data for dashboard
     *
     * @param string $departmentName - The department name
     * @param int $year - Optional year filter (defaults to current year)
     * @return array - Department absence statistics
     */
    public function getDepartmentAbsenceData($departmentName, $year = null) {
        try {
            if (!$departmentName) {
                return null;
            }

            if ($year === null) {
                $year = date('Y');
            }

            // Get total users in department
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as total
                FROM users
                WHERE departement = ? AND actif = 1
            ");
            $stmt->execute([$departmentName]);
            $totalUsers = $stmt->fetchColumn();

            if ($totalUsers == 0) {
                return null;
            }

            // Get total leave days for the department
            $stmt = $this->db->prepare("
                SELECT
                    SUM(DATEDIFF(d.date_fin, d.date_debut) + 1) as total_days,
                    COUNT(DISTINCT d.user_id) as users_with_leave
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE u.departement = ?
                AND d.statut = 'approuvee'
                AND YEAR(d.date_debut) = ?
            ");
            $stmt->execute([$departmentName, $year]);
            $result = $stmt->fetch();

            $totalDays = $result['total_days'] ?: 0;
            $usersWithLeave = $result['users_with_leave'] ?: 0;

            // Get users currently on leave
            $today = date('Y-m-d');
            $stmt = $this->db->prepare("
                SELECT COUNT(DISTINCT d.user_id) as count
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE u.departement = ?
                AND d.statut = 'approuvee'
                AND ? BETWEEN d.date_debut AND d.date_fin
            ");
            $stmt->execute([$departmentName, $today]);
            $currentlyOnLeave = $stmt->fetchColumn();

            // Get upcoming absences (next 30 days)
            $startDate = date('Y-m-d', strtotime('+1 day'));
            $endDate = date('Y-m-d', strtotime('+30 days'));
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE u.departement = ?
                AND d.statut = 'approuvee'
                AND (
                    (d.date_debut BETWEEN ? AND ?) OR
                    (d.date_fin BETWEEN ? AND ?) OR
                    (d.date_debut <= ? AND d.date_fin >= ?)
                )
            ");
            $stmt->execute([$departmentName, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate]);
            $upcomingAbsences = $stmt->fetchColumn();

            return [
                'total_users' => $totalUsers,
                'users_with_leave' => $usersWithLeave,
                'total_days' => $totalDays,
                'average_days_per_user' => $totalUsers > 0 ? round($totalDays / $totalUsers, 1) : 0,
                'currently_on_leave' => $currentlyOnLeave,
                'upcoming_absences' => $upcomingAbsences,
                'department_name' => $departmentName
            ];
        } catch (PDOException $e) {
            error_log('Database error in getDepartmentAbsenceData: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get all leave requests with user information and optional search/filters
     *
     * @param string $search - Optional search term
     * @param string $status - Optional status filter (approved, pending, rejected)
     * @param string $type - Optional type filter (paid, unpaid, sick, exceptional)
     * @return array - Array of leave requests with user information
     */
    public function getAllDemandes($search = '', $status = '', $type = '') {
        try {
            $query = "
                SELECT
                    d.id, d.type, d.date_debut, d.date_fin, d.statut,
                    d.date_demande, d.motif, d.valide_par, d.reference_demande,
                    u.nom as user_nom, u.prenom as user_prenom, u.id as user_id
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE 1=1
            ";

            $params = [];

            // Add search condition if provided
            if (!empty($search)) {
                $query .= " AND (
                    u.nom LIKE ? OR
                    u.prenom LIKE ? OR
                    d.motif LIKE ? OR
                    d.reference_demande LIKE ?
                )";
                $searchTerm = "%$search%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            // Add status filter if provided
            if (!empty($status)) {
                switch ($status) {
                    case 'approved':
                        $query .= " AND d.statut = 'approuvee'";
                        break;
                    case 'pending':
                        $query .= " AND (d.statut = 'en_attente_responsable' OR d.statut = 'en_attente_planificateur')";
                        break;
                    case 'rejected':
                        $query .= " AND d.statut = 'refusee'";
                        break;
                    case 'cancelled':
                        $query .= " AND d.statut = 'annulee'";
                        break;
                }
            }

            // Add type filter if provided
            if (!empty($type)) {
                switch ($type) {
                    case 'paid':
                        $query .= " AND d.type = 'payé'";
                        break;
                    case 'unpaid':
                        $query .= " AND d.type = 'sans solde'";
                        break;
                    case 'sick':
                        $query .= " AND d.type = 'maladie'";
                        break;
                    case 'exceptional':
                        $query .= " AND d.type = 'exceptionnel'";
                        break;
                }
            }

            $query .= " ORDER BY d.date_demande DESC";

            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            $demandes = $stmt->fetchAll();

            // Format the status values to match the view expectations
            foreach ($demandes as &$demande) {
                $demande['statut'] = $this->formatStatus($demande['statut']);
                // Format the type values
                $demande['type'] = $this->formatLeaveType($demande['type']);
            }

            return $demandes;
        } catch (PDOException $e) {
            // Log the error
            error_log('Database error in getAllDemandes: ' . $e->getMessage());
            return [];
        }
    }

    // Format leave type for display
    public function formatLeaveType($type) {
        switch (strtolower($type)) {
            case 'payé':
            case 'paye':
            case 'paid':
            case 'conge_paye':
                return 'Congé payé';
            case 'sans solde':
            case 'unpaid':
            case 'conge_sans_solde':
                return 'Congé sans solde';
            case 'maladie':
            case 'sick':
                return 'Congé maladie';
            case 'familial':
            case 'family':
                return 'Congé familial';
            case 'exceptionnel':
            case 'special':
                return 'Congé exceptionnel';
            case 'training':
            case 'formation':
                return 'Formation';
            default:
                return ucfirst($type);
        }
    }

    // Get all upcoming leaves for all users (for planificateur)
    public function getAllUpcomingLeaves($limit = 20) {
        try {
            $stmt = $this->db->prepare("
                SELECT
                    d.id, d.type, d.date_debut, d.date_fin, d.statut, d.reference_demande,
                    DATEDIFF(d.date_fin, d.date_debut) + 1 as duree,
                    u.nom as user_nom, u.prenom as user_prenom, u.id as user_id
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE d.date_debut >= CURDATE()
                AND (d.statut = 'approuvee' OR d.statut = 'en_attente_responsable' OR d.statut = 'en_attente_planificateur')
                ORDER BY d.date_debut ASC
                LIMIT " . intval($limit) . "
            ");
            $stmt->execute();
            $absences = $stmt->fetchAll();

            // Format the data for display
            foreach ($absences as &$absence) {
                $absence['type_formatted'] = $this->formatLeaveType($absence['type']);
                $absence['statut_formatted'] = $this->formatStatus($absence['statut']);
                $absence['nom_complet'] = $absence['user_prenom'] . ' ' . $absence['user_nom'];
            }

            return $absences;
        } catch (PDOException $e) {
            // Log the error
            error_log('Database error in getAllUpcomingLeaves: ' . $e->getMessage());
            return [];
        }
    }

    // Format status for display
    private function formatStatus($status) {
        switch ($status) {
            case 'en_attente_responsable':
                return 'En attente responsable';
            case 'en_attente_planificateur':
                return 'En attente planificateur';
            case 'approuvee':
                return 'Approuvée';
            case 'refusee':
                return 'Rejetée';
            case 'annulee':
                return 'Annulée';
            // Legacy status support
            case 'acceptée':
                return 'Approuvée';
            case 'en cours':
                return 'En attente';
            case 'refusée':
                return 'Rejetée';
            default:
                return ucfirst($status);
        }
    }

    /**
     * Get all approved leave requests for the planning calendar
     *
     * @param string $startDate - Start date in Y-m-d format
     * @param string $endDate - End date in Y-m-d format
     * @param string $department - Optional department filter
     * @return array - Array of leave requests formatted for FullCalendar
     */
    public function getAllApprovedDemandes($startDate = null, $endDate = null, $department = null) {
        try {
            // If no dates provided, default to current month
            if (!$startDate) {
                $startDate = date('Y-m-01'); // First day of current month
            }
            if (!$endDate) {
                $endDate = date('Y-m-t'); // Last day of current month
            }

            $query = "
                SELECT
                    d.id, d.type, d.date_debut, d.date_fin, d.statut, d.reference_demande,
                    u.id as user_id, u.nom, u.prenom, u.departement
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE d.statut = 'approuvee'
                AND (
                    (d.date_debut BETWEEN ? AND ?) OR
                    (d.date_fin BETWEEN ? AND ?) OR
                    (d.date_debut <= ? AND d.date_fin >= ?)
                )
            ";

            $params = [$startDate, $endDate, $startDate, $endDate, $startDate, $endDate];

            // Add department filter if provided
            if ($department && $department !== 'all') {
                $query .= " AND u.departement = ?";
                $params[] = $department;
            }

            $query .= " ORDER BY d.date_debut ASC";

            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            $demandes = $stmt->fetchAll();

            // Format for FullCalendar
            $events = [];
            foreach ($demandes as $demande) {
                $eventType = $this->getEventTypeFromLeaveType($demande['type']);
                $events[] = [
                    'id' => $demande['id'],
                    'title' => $this->formatLeaveType($demande['type']) . ' - ' . $demande['prenom'] . ' ' . $demande['nom'],
                    'start' => $demande['date_debut'],
                    'end' => date('Y-m-d', strtotime($demande['date_fin'] . ' +1 day')), // FullCalendar uses exclusive end dates
                    'type' => $eventType,
                    'userId' => $demande['user_id'],
                    'department' => $demande['departement'] ?? 'Non spécifié'
                ];
            }

            return $events;
        } catch (PDOException $e) {
            error_log('Database error in getAllApprovedDemandes: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get team availability data for a date range
     *
     * @param string $startDate - Start date in Y-m-d format
     * @param string $endDate - End date in Y-m-d format
     * @param string $department - Optional department filter
     * @return array - Array of availability data by date
     */
    public function getTeamAvailability($startDate = null, $endDate = null, $department = null) {
        try {
            // If no dates provided, default to next 7 days
            if (!$startDate) {
                $startDate = date('Y-m-d');
            }
            if (!$endDate) {
                $endDate = date('Y-m-d', strtotime('+7 days'));
            }

            // Get total number of users
            $userQuery = "SELECT COUNT(*) as total FROM users WHERE 1=1";
            $userParams = [];

            if ($department && $department !== 'all') {
                $userQuery .= " AND departement = ?";
                $userParams[] = $department;
            }

            $stmt = $this->db->prepare($userQuery);
            $stmt->execute($userParams);
            $totalUsers = $stmt->fetch()['total'];

            // Generate date range
            $dateRange = [];
            $currentDate = new DateTime($startDate);
            $endDateTime = new DateTime($endDate);

            while ($currentDate <= $endDateTime) {
                $dateStr = $currentDate->format('Y-m-d');
                $dateRange[$dateStr] = [
                    'date' => $dateStr,
                    'available' => $totalUsers,
                    'unavailable' => 0
                ];
                $currentDate->modify('+1 day');
            }

            // Get unavailable users for each date
            $query = "
                SELECT
                    d.date_debut, d.date_fin,
                    COUNT(DISTINCT d.user_id) as unavailable_count
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE d.statut = 'approuvee'
                AND (
                    (d.date_debut <= ? AND d.date_fin >= ?) OR
                    (d.date_debut BETWEEN ? AND ?)
                )
            ";

            $params = [$endDate, $startDate, $startDate, $endDate];

            if ($department && $department !== 'all') {
                $query .= " AND u.departement = ?";
                $params[] = $department;
            }

            $query .= " GROUP BY d.date_debut, d.date_fin";

            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            $unavailableData = $stmt->fetchAll();

            // Update date range with unavailable counts
            foreach ($unavailableData as $data) {
                $leaveStart = new DateTime($data['date_debut']);
                $leaveEnd = new DateTime($data['date_fin']);

                $currentDate = clone $leaveStart;
                while ($currentDate <= $leaveEnd) {
                    $dateStr = $currentDate->format('Y-m-d');
                    if (isset($dateRange[$dateStr])) {
                        $dateRange[$dateStr]['unavailable'] += $data['unavailable_count'];
                        $dateRange[$dateStr]['available'] = $totalUsers - $dateRange[$dateStr]['unavailable'];
                    }
                    $currentDate->modify('+1 day');
                }
            }

            return array_values($dateRange);
        } catch (PDOException $e) {
            error_log('Database error in getTeamAvailability: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get department statistics for leave requests
     *
     * @param int $year - Optional year filter (defaults to current year)
     * @return array - Array of department statistics
     */
    public function getDepartmentStats($year = null) {
        try {
            if (!$year) {
                $year = date('Y');
            }

            $query = "
                SELECT
                    u.departement,
                    COUNT(DISTINCT u.id) as user_count,
                    COUNT(d.id) as leave_count,
                    SUM(DATEDIFF(d.date_fin, d.date_debut) + 1) as total_days,
                    ROUND(SUM(DATEDIFF(d.date_fin, d.date_debut) + 1) / COUNT(DISTINCT u.id), 1) as avg_days
                FROM users u
                LEFT JOIN demandes_conges d ON u.id = d.user_id AND YEAR(d.date_debut) = ? AND d.statut = 'acceptee'
                WHERE u.departement IS NOT NULL AND u.departement != ''
                GROUP BY u.departement
                ORDER BY avg_days DESC
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$year]);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log('Database error in getDepartmentStats: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get leave type statistics
     *
     * @param int $year - Optional year filter (defaults to current year)
     * @return array - Array of leave type statistics
     */
    public function getLeaveTypeStats($year = null) {
        try {
            if (!$year) {
                $year = date('Y');
            }

            $query = "
                SELECT
                    type,
                    COUNT(*) as count
                FROM demandes_conges
                WHERE YEAR(date_debut) = ? AND statut = 'acceptée'
                GROUP BY type
            ";

            $stmt = $this->db->prepare($query);
            $stmt->execute([$year]);
            $results = $stmt->fetchAll();

            // Calculate total for percentages
            $total = 0;
            foreach ($results as $row) {
                $total += $row['count'];
            }

            // Add percentage to each row
            $stats = [];
            foreach ($results as $row) {
                $percentage = $total > 0 ? round(($row['count'] / $total) * 100) : 0;
                $stats[] = [
                    'type' => $this->formatLeaveType($row['type']),
                    'count' => $row['count'],
                    'percentage' => $percentage
                ];
            }

            return $stats;
        } catch (PDOException $e) {
            error_log('Database error in getLeaveTypeStats: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get monthly statistics for all users
     *
     * @param int $year - Optional year filter (defaults to current year)
     * @return array - Array of monthly statistics
     */
    public function getDetailedMonthlyStats($year = null) {
        try {
            if (!$year) {
                $year = date('Y');
            }

            $months = [
                'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
            ];

            // Initialize results array with all months
            $monthlyStats = [];
            for ($i = 0; $i < 12; $i++) {
                $monthlyStats[] = [
                    'month' => $months[$i],
                    'month_num' => $i + 1,
                    'requests' => 0,
                    'approvedDays' => 0,
                    'rejectedDays' => 0
                ];
            }

            // Get request counts by month
            $requestQuery = "
                SELECT
                    MONTH(date_demande) as month,
                    COUNT(*) as request_count
                FROM demandes_conges
                WHERE YEAR(date_demande) = ?
                GROUP BY MONTH(date_demande)
            ";

            $stmt = $this->db->prepare($requestQuery);
            $stmt->execute([$year]);
            $requestResults = $stmt->fetchAll();

            foreach ($requestResults as $row) {
                $monthIndex = $row['month'] - 1;
                if (isset($monthlyStats[$monthIndex])) {
                    $monthlyStats[$monthIndex]['requests'] = $row['request_count'];
                }
            }

            // Get approved days by month
            $approvedQuery = "
                SELECT
                    MONTH(date_debut) as month,
                    SUM(DATEDIFF(date_fin, date_debut) + 1) as approved_days
                FROM demandes_conges
                WHERE YEAR(date_debut) = ? AND statut = 'acceptee'
                GROUP BY MONTH(date_debut)
            ";

            $stmt = $this->db->prepare($approvedQuery);
            $stmt->execute([$year]);
            $approvedResults = $stmt->fetchAll();

            foreach ($approvedResults as $row) {
                $monthIndex = $row['month'] - 1;
                if (isset($monthlyStats[$monthIndex])) {
                    $monthlyStats[$monthIndex]['approvedDays'] = $row['approved_days'];
                }
            }

            // Get rejected days by month
            $rejectedQuery = "
                SELECT
                    MONTH(date_debut) as month,
                    SUM(DATEDIFF(date_fin, date_debut) + 1) as rejected_days
                FROM demandes_conges
                WHERE YEAR(date_debut) = ? AND statut = 'refusee'
                GROUP BY MONTH(date_debut)
            ";

            $stmt = $this->db->prepare($rejectedQuery);
            $stmt->execute([$year]);
            $rejectedResults = $stmt->fetchAll();

            foreach ($rejectedResults as $row) {
                $monthIndex = $row['month'] - 1;
                if (isset($monthlyStats[$monthIndex])) {
                    $monthlyStats[$monthIndex]['rejectedDays'] = $row['rejected_days'];
                }
            }

            return $monthlyStats;
        } catch (PDOException $e) {
            error_log('Database error in getDetailedMonthlyStats: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get dashboard statistics for planificateur
     *
     * @return array - Array of dashboard statistics
     */
    public function getPlanificateurDashboardStats() {
        try {
            // Get pending requests count
            $pendingQuery = "
                SELECT COUNT(*) as count
                FROM demandes_conges
                WHERE (statut = 'en_attente_responsable' OR statut = 'en_attente_planificateur')
            ";
            $stmt = $this->db->prepare($pendingQuery);
            $stmt->execute();
            $pendingRequests = $stmt->fetch()['count'];

            // Get approved requests count
            $approvedQuery = "
                SELECT COUNT(*) as count
                FROM demandes_conges
                WHERE statut = 'acceptée'
            ";
            $stmt = $this->db->prepare($approvedQuery);
            $stmt->execute();
            $approvedRequests = $stmt->fetch()['count'];

            // Get rejected requests count
            $rejectedQuery = "
                SELECT COUNT(*) as count
                FROM demandes_conges
                WHERE statut = 'refusée'
            ";
            $stmt = $this->db->prepare($rejectedQuery);
            $stmt->execute();
            $rejectedRequests = $stmt->fetch()['count'];

            return [
                'pendingRequests' => $pendingRequests,
                'approvedRequests' => $approvedRequests,
                'rejectedRequests' => $rejectedRequests
            ];
        } catch (PDOException $e) {
            error_log('Database error in getPlanificateurDashboardStats: ' . $e->getMessage());
            return [
                'pendingRequests' => 0,
                'approvedRequests' => 0,
                'rejectedRequests' => 0
            ];
        }
    }



    /**
     * Get statistics for admin dashboard
     *
     * @return array - Statistics data
     */
    public function getAdminStatistics() {
        try {
            // Total demandes
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM demandes_conges");
            $stmt->execute();
            $totalDemandes = $stmt->fetchColumn();

            // Demandes by status
            $stmt = $this->db->prepare("
                SELECT statut, COUNT(*) as count
                FROM demandes_conges
                GROUP BY statut
            ");
            $stmt->execute();
            $demandesByStatus = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // Demandes by month
            $stmt = $this->db->prepare("
                SELECT
                    MONTH(date_demande) as month,
                    COUNT(*) as count
                FROM demandes_conges
                WHERE YEAR(date_demande) = YEAR(CURDATE())
                GROUP BY MONTH(date_demande)
            ");
            $stmt->execute();
            $demandesByMonth = [];
            foreach ($stmt->fetchAll() as $row) {
                $demandesByMonth[$row['month']] = $row['count'];
            }

            // Fill in missing months with 0
            for ($i = 1; $i <= 12; $i++) {
                if (!isset($demandesByMonth[$i])) {
                    $demandesByMonth[$i] = 0;
                }
            }
            ksort($demandesByMonth);

            return [
                'totalDemandes' => $totalDemandes,
                'demandesByStatus' => $demandesByStatus,
                'demandesByMonth' => array_values($demandesByMonth)
            ];
        } catch (PDOException $e) {
            error_log('Database error in getAdminStatistics: ' . $e->getMessage());
            return [
                'totalDemandes' => 0,
                'demandesByStatus' => [],
                'demandesByMonth' => array_fill(0, 12, 0)
            ];
        }
    }

    /**
     * Get statistics for a responsable
     *
     * @param int $responsableId - The ID of the responsable
     * @return array - Statistics data
     */
    public function getResponsableStatistics($responsableId) {
        try {
            // Get the responsable
            $userModel = new UserModel();
            $responsable = $userModel->getUserById($responsableId);

            if (!$responsable || $responsable['role'] !== 'responsable') {
                return [];
            }

            $responsableDept = $responsable['departement'];

            // Total demandes from employees managed by this responsable
            $stmt = $this->db->prepare("
                SELECT COUNT(*)
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE u.manager_id = ?
            ");
            $stmt->execute([$responsableId]);
            $totalDemandes = $stmt->fetchColumn();

            // Demandes by status
            $stmt = $this->db->prepare("
                SELECT d.statut, COUNT(*) as count
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE u.manager_id = ?
                GROUP BY d.statut
            ");
            $stmt->execute([$responsableId]);
            $demandesByStatus = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // Extract individual status counts
            $demandesApprouvees = $demandesByStatus['acceptée'] ?? 0;
            $demandesRejetees = $demandesByStatus['refusée'] ?? 0;
            $demandesEnAttente = ($demandesByStatus['en_attente_responsable'] ?? 0) + ($demandesByStatus['en_attente_planificateur'] ?? 0);

            // Calculate approval percentage
            $pourcentageApprouve = $totalDemandes > 0 ? round(($demandesApprouvees / $totalDemandes) * 100, 1) : 0;

            // Demandes by month
            $stmt = $this->db->prepare("
                SELECT
                    MONTH(d.date_demande) as month,
                    COUNT(*) as count
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE u.manager_id = ?
                AND YEAR(d.date_demande) = YEAR(CURDATE())
                GROUP BY MONTH(d.date_demande)
            ");
            $stmt->execute([$responsableId]);
            $demandesByMonth = [];
            foreach ($stmt->fetchAll() as $row) {
                $demandesByMonth[$row['month']] = $row['count'];
            }

            // Fill in missing months with 0
            for ($i = 1; $i <= 12; $i++) {
                if (!isset($demandesByMonth[$i])) {
                    $demandesByMonth[$i] = 0;
                }
            }
            ksort($demandesByMonth);

            // Calculate average team availability
            $stmt = $this->db->prepare("
                SELECT COUNT(*)
                FROM users
                WHERE manager_id = ? AND role = 'employe' AND actif = 1
            ");
            $stmt->execute([$responsableId]);
            $totalTeamMembers = $stmt->fetchColumn();

            // Get team members details
            $teamMembers = $userModel->getTeamMembers($responsableId);

            // Get average number of people on leave per day for the next 30 days
            $stmt = $this->db->prepare("
                SELECT
                    DATE(d.date_debut + INTERVAL n DAY) as day,
                    COUNT(DISTINCT d.user_id) as absent_count
                FROM
                    demandes_conges d
                    JOIN users u ON d.user_id = u.id,
                    (
                        SELECT a.N + b.N * 10 + c.N * 100 as n
                        FROM
                            (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a,
                            (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b,
                            (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c
                    ) as numbers
                WHERE
                    u.manager_id = ?
                    AND d.statut = 'acceptee'
                    AND DATE(d.date_debut + INTERVAL n DAY) BETWEEN d.date_debut AND d.date_fin
                    AND DATE(d.date_debut + INTERVAL n DAY) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
                GROUP BY
                    DATE(d.date_debut + INTERVAL n DAY)
            ");
            $stmt->execute([$responsableId]);
            $absenteesByDay = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // Calculate average availability
            $totalDays = 30;
            $totalAbsences = 0;

            foreach ($absenteesByDay as $day => $count) {
                $totalAbsences += $count;
            }

            $averageAbsences = $totalAbsences / $totalDays;
            $averageAvailability = $totalTeamMembers > 0 ? ($totalTeamMembers - $averageAbsences) / $totalTeamMembers * 100 : 0;

            // Get demandes by type
            $stmt = $this->db->prepare("
                SELECT d.type, COUNT(*) as count
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE u.manager_id = ?
                GROUP BY d.type
            ");
            $stmt->execute([$responsableId]);
            $demandesByType = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // Format leave types for display
            $formattedDemandesByType = [];
            foreach ($demandesByType as $type => $count) {
                $formattedDemandesByType[$this->formatLeaveType($type)] = $count;
            }

            // Get recent decisions (last 5 approved/rejected)
            $stmt = $this->db->prepare("
                SELECT
                    d.id, d.user_id, d.type, d.date_debut, d.date_fin, d.statut,
                    d.date_decision, d.motif_rejet, u.nom, u.prenom
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE u.manager_id = ?
                AND d.statut IN ('acceptée', 'refusée')
                AND d.date_decision IS NOT NULL
                ORDER BY d.date_decision DESC
                LIMIT 5
            ");
            $stmt->execute([$responsableId]);
            $recentDecisions = $stmt->fetchAll();

            // Format the recent decisions
            foreach ($recentDecisions as &$decision) {
                $decision['type'] = $this->formatLeaveType($decision['type']);
                $decision['date_debut_formatted'] = date('d/m/Y', strtotime($decision['date_debut']));
                $decision['date_fin_formatted'] = date('d/m/Y', strtotime($decision['date_fin']));
                $decision['date_decision_formatted'] = date('d/m/Y H:i', strtotime($decision['date_decision']));

                // Calculate duration
                $dateDebut = new DateTime($decision['date_debut']);
                $dateFin = new DateTime($decision['date_fin']);
                $interval = $dateDebut->diff($dateFin);
                $decision['duree'] = $interval->days + 1; // +1 because the end date is inclusive
            }

            // Get upcoming absences (next 30 days)
            $stmt = $this->db->prepare("
                SELECT
                    d.id, d.user_id, d.type, d.date_debut, d.date_fin,
                    u.nom, u.prenom
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE u.manager_id = ?
                AND d.statut = 'acceptee'
                AND d.date_debut BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
                ORDER BY d.date_debut ASC
                LIMIT 10
            ");
            $stmt->execute([$responsableId]);
            $upcomingAbsences = $stmt->fetchAll();

            // Format the upcoming absences
            foreach ($upcomingAbsences as &$absence) {
                $absence['type'] = $this->formatLeaveType($absence['type']);
                $absence['date_debut_formatted'] = date('d/m/Y', strtotime($absence['date_debut']));
                $absence['date_fin_formatted'] = date('d/m/Y', strtotime($absence['date_fin']));

                // Calculate duration
                $dateDebut = new DateTime($absence['date_debut']);
                $dateFin = new DateTime($absence['date_fin']);
                $interval = $dateDebut->diff($dateFin);
                $absence['duree'] = $interval->days + 1; // +1 because the end date is inclusive

                // Calculate days until start
                $today = new DateTime();
                $daysUntil = $today->diff($dateDebut)->days;
                $absence['days_until'] = $daysUntil;
            }

            return [
                'totalDemandes' => $totalDemandes,
                'demandesApprouvees' => $demandesApprouvees,
                'demandesRejetees' => $demandesRejetees,
                'demandesEnAttente' => $demandesEnAttente,
                'demandesByMonth' => array_values($demandesByMonth),
                'demandesByType' => $formattedDemandesByType,
                'averageTeamAvailability' => $averageAvailability,
                'membresEquipe' => $teamMembers,
                'pourcentageApprouve' => $pourcentageApprouve,
                'recentDecisions' => $recentDecisions,
                'upcomingAbsences' => $upcomingAbsences,
                'totalTeamMembers' => $totalTeamMembers,
                'departement' => $responsableDept
            ];
        } catch (PDOException $e) {
            error_log('Database error in getResponsableStatistics: ' . $e->getMessage());
            return [
                'totalDemandes' => 0,
                'demandesApprouvees' => 0,
                'demandesRejetees' => 0,
                'demandesEnAttente' => 0,
                'demandesByMonth' => array_fill(0, 12, 0),
                'demandesByType' => [],
                'averageTeamAvailability' => 0,
                'membresEquipe' => [],
                'pourcentageApprouve' => 0,
                'recentDecisions' => [],
                'upcomingAbsences' => [],
                'totalTeamMembers' => 0,
                'departement' => ''
            ];
        }
    }

    /**
     * Get team availability data for a specific month
     *
     * @param int $responsableId - The ID of the responsable
     * @param string $month - Month in format YYYY-MM (default: current month)
     * @return array - Availability data
     */
    public function getTeamAvailabilityData($responsableId, $month = null) {
        try {
            // Get the responsable
            $userModel = new UserModel();
            $responsable = $userModel->getUserById($responsableId);

            if (!$responsable || $responsable['role'] !== 'responsable') {
                error_log("Invalid responsable or role: ID=$responsableId");
                return [];
            }

            $responsableDept = $responsable['departement'];

            // If month is not provided, use current month
            if (!$month) {
                $month = date('Y-m');
            }

            error_log("Getting team availability for responsable ID=$responsableId, month=$month");

            // Get the first and last day of the month
            $firstDay = date('Y-m-01', strtotime($month));
            $lastDay = date('Y-m-t', strtotime($month));

            // Get team members
            $teamMembers = $userModel->getTeamMembers($responsableId);
            $totalTeamMembers = count($teamMembers);

            error_log("Total team members for responsable ID=$responsableId: $totalTeamMembers");

            // Get member IDs for easier reference
            $teamMemberIds = array_column($teamMembers, 'id');

            if (empty($teamMemberIds)) {
                error_log("No team members found for responsable ID=$responsableId");
                return [
                    'month' => date('F Y', strtotime($month)),
                    'days' => [],
                    'members' => [],
                    'holidays' => []
                ];
            }

            // Get holidays in the month
            $jourFerieModel = new JourFerieModel();
            $holidays = $jourFerieModel->getJoursFeriesByMonth(date('m', strtotime($month)));

            // Create a lookup array for holidays
            $holidayDates = [];
            foreach ($holidays as $holiday) {
                $holidayDate = date('Y-m-d', strtotime($holiday['date']));
                $holidayDates[$holidayDate] = $holiday['nom'];
            }

            error_log("Found " . count($holidayDates) . " holidays for month $month");

            // Get all absences for the month
            $stmt = $this->db->prepare("
                SELECT
                    d.id, d.user_id, d.type, d.date_debut, d.date_fin, d.statut,
                    u.nom, u.prenom
                FROM demandes_conges d
                JOIN users u ON d.user_id = u.id
                WHERE u.manager_id = ?
                AND d.statut = 'acceptée'
                AND (
                    (d.date_debut BETWEEN ? AND ?) OR
                    (d.date_fin BETWEEN ? AND ?) OR
                    (d.date_debut <= ? AND d.date_fin >= ?)
                )
            ");
            $stmt->execute([$responsableId, $firstDay, $lastDay, $firstDay, $lastDay, $firstDay, $lastDay]);
            $absences = $stmt->fetchAll();

            error_log("Found " . count($absences) . " absences for month $month");

            // Process absences to get dates for each user
            $userAbsenceDates = [];
            $absenceTypes = [];

            foreach ($absences as $absence) {
                $userId = $absence['user_id'];
                $startDate = new DateTime($absence['date_debut']);
                $endDate = new DateTime($absence['date_fin']);
                $leaveType = $absence['type'];

                // Initialize arrays if needed
                if (!isset($userAbsenceDates[$userId])) {
                    $userAbsenceDates[$userId] = [];
                }
                if (!isset($absenceTypes[$userId])) {
                    $absenceTypes[$userId] = [];
                }

                // Add all dates between start and end to the user's absence dates
                $currentDate = clone $startDate;
                while ($currentDate <= $endDate) {
                    $dateStr = $currentDate->format('Y-m-d');
                    $userAbsenceDates[$userId][] = $dateStr;
                    $absenceTypes[$userId][$dateStr] = $leaveType;
                    $currentDate->modify('+1 day');
                }
            }

            // Build calendar data
            $days = [];
            $currentDate = new DateTime($firstDay);
            $lastDayDate = new DateTime($lastDay);

            error_log("Building calendar from $firstDay to $lastDay");

            while ($currentDate <= $lastDayDate) {
                $currentDateStr = $currentDate->format('Y-m-d');
                $isWeekend = in_array($currentDate->format('N'), [6, 7]); // Saturday or Sunday
                $isHoliday = isset($holidayDates[$currentDateStr]);

                // Count absent members for this day
                $absentMembers = 0;
                $absentMembersList = [];

                foreach ($userAbsenceDates as $userId => $userAbsenceDatesList) {
                    if (in_array($currentDateStr, $userAbsenceDatesList)) {
                        $absentMembers++;

                        // Find the member details
                        foreach ($teamMembers as $member) {
                            if ($member['id'] == $userId) {
                                // Format the leave type for display
                                $leaveType = $absenceTypes[$userId][$currentDateStr] ?? 'Congé';
                                $formattedLeaveType = $this->formatLeaveType($leaveType);

                                $absentMembersList[] = [
                                    'id' => $userId,
                                    'nom' => $member['nom'],
                                    'prenom' => $member['prenom'],
                                    'type_conge' => $formattedLeaveType
                                ];
                                break;
                            }
                        }
                    }
                }

                // Add day to calendar data
                $days[] = [
                    'date' => $currentDateStr,
                    'dayOfMonth' => $currentDate->format('j'),
                    'isWeekend' => $isWeekend,
                    'isHoliday' => $isHoliday,
                    'holidayName' => $isHoliday ? $holidayDates[$currentDateStr] : null,
                    'totalMembers' => $totalTeamMembers,
                    'absentMembers' => $absentMembers,
                    'absentMembersList' => $absentMembersList
                ];

                // Move to next day
                $currentDate->modify('+1 day');
            }

            return [
                'month' => date('F Y', strtotime($month)),
                'days' => $days,
                'members' => $teamMembers,
                'holidays' => $holidays
            ];
        } catch (PDOException $e) {
            error_log('Database error in getTeamAvailabilityData: ' . $e->getMessage());
            return [
                'month' => date('F Y', strtotime($month ?? date('Y-m'))),
                'days' => [],
                'members' => [],
                'holidays' => []
            ];
        } catch (Exception $e) {
            error_log('Error in getTeamAvailabilityData: ' . $e->getMessage());
            return [
                'month' => date('F Y', strtotime($month ?? date('Y-m'))),
                'days' => [],
                'members' => [],
                'holidays' => []
            ];
        }
    }



    /**
     * Helper method to map leave types to event types for the calendar
     */
    private function getEventTypeFromLeaveType($type) {
        switch (strtolower($type)) {
            case 'payé':
            case 'paye':
                return 'vacation';
            case 'sans solde':
                return 'unpaid';
            case 'maladie':
                return 'sick';
            case 'familial':
                return 'family';
            case 'formation':
                return 'training';
            case 'exceptionnel':
                return 'special';
            default:
                return 'other';
        }
    }

    /**
     * Get user's leave balances
     *
     * @param int $userId The user ID
     * @return array Array containing leave balances for different types
     */
    public function getUserLeaveBalances($userId) {
        // Use the LeaveBalanceModel to get the balances
        $leaveBalanceModel = new LeaveBalanceModel();
        return $leaveBalanceModel->getUserLeaveBalances($userId);
    }

    /**
     * Calculate the number of days for a leave request
     *
     * @param string $dateDebut Start date (YYYY-MM-DD)
     * @param string $dateFin End date (YYYY-MM-DD)
     * @param bool $demiJournee Whether it's a half-day request
     * @param string $demiType Type of half-day (matin, apres-midi, matin,apres-midi)
     * @return float Number of days
     */
    public function calculateLeaveDays($dateDebut, $dateFin, $demiJournee = false, $demiType = null) {
        $start = new DateTime($dateDebut);
        $end = new DateTime($dateFin);
        $interval = $start->diff($end);
        $days = $interval->days + 1; // +1 because the end date is inclusive

        // Adjust for half-day if applicable
        if ($demiJournee) {
            if ($demiType && strpos($demiType, ',') !== false) {
                // Both start and end dates are half-days
                $days -= 1.0;
            } else {
                // Only one half-day
                $days -= 0.5;
            }
        }

        // TODO: Consider weekends and holidays for more accurate calculation

        return $days;
    }

    /**
     * Get detailed leave statistics for a specific user
     *
     * @param int $userId - The ID of the user
     * @return array - Statistics data
     */
    public function getUserLeaveStatistics($userId) {
        try {
            // Total demandes
            $stmt = $this->db->prepare("SELECT COUNT(*) as total FROM demandes_conges WHERE user_id = ?");
            $stmt->execute([$userId]);
            $totalDemandes = $stmt->fetch()['total'];

            // Approved demandes
            $stmt = $this->db->prepare("SELECT COUNT(*) as approved FROM demandes_conges WHERE user_id = ? AND statut = 'acceptee'");
            $stmt->execute([$userId]);
            $approvedDemandes = $stmt->fetch()['approved'];

            // Rejected demandes
            $stmt = $this->db->prepare("SELECT COUNT(*) as rejected FROM demandes_conges WHERE user_id = ? AND statut = 'refusee'");
            $stmt->execute([$userId]);
            $rejectedDemandes = $stmt->fetch()['rejected'];

            // Pending demandes
            $stmt = $this->db->prepare("SELECT COUNT(*) as pending FROM demandes_conges WHERE user_id = ? AND (statut = 'en_attente_responsable' OR statut = 'en_attente_planificateur')");
            $stmt->execute([$userId]);
            $pendingDemandes = $stmt->fetch()['pending'];

            // Cancelled demandes
            $stmt = $this->db->prepare("SELECT COUNT(*) as cancelled FROM demandes_conges WHERE user_id = ? AND statut = 'annulee'");
            $stmt->execute([$userId]);
            $cancelledDemandes = $stmt->fetch()['cancelled'];

            // Number of leave days by type
            $stmt = $this->db->prepare("
                SELECT
                    type,
                    COUNT(*) as request_count,
                    SUM(DATEDIFF(date_fin, date_debut) + 1) as total_days
                FROM demandes_conges
                WHERE user_id = ? AND statut = 'acceptee'
                GROUP BY type
            ");
            $stmt->execute([$userId]);
            $daysbyType = [];
            foreach ($stmt->fetchAll() as $row) {
                $daysbyType[$row['type']] = [
                    'count' => $row['request_count'],
                    'days' => $row['total_days']
                ];
            }

            // Current or next leave period
            $stmt = $this->db->prepare("
                SELECT
                    date_debut,
                    date_fin,
                    type,
                    DATEDIFF(date_fin, date_debut) + 1 as duration
                FROM demandes_conges
                WHERE
                    user_id = ? AND
                    statut = 'acceptee' AND
                    (
                        (CURRENT_DATE BETWEEN date_debut AND date_fin) OR
                        (date_debut > CURRENT_DATE)
                    )
                ORDER BY date_debut ASC
                LIMIT 1
            ");
            $stmt->execute([$userId]);
            $currentOrNextLeave = $stmt->fetch();

            return [
                'total' => $totalDemandes,
                'approved' => $approvedDemandes,
                'rejected' => $rejectedDemandes,
                'pending' => $pendingDemandes,
                'cancelled' => $cancelledDemandes,
                'days_by_type' => $daysbyType,
                'current_or_next_leave' => $currentOrNextLeave
            ];
        } catch (PDOException $e) {
            error_log('Database error in getUserLeaveStatistics: ' . $e->getMessage());
            return [];
        }
    }
}
