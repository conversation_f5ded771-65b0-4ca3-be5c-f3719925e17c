document.addEventListener('DOMContentLoaded', function() {
    // Get form elements
    const dateDebut = document.getElementById('date_debut');
    const dateFin = document.getElementById('date_fin');
    const demiJourDebut = document.getElementById('demi_jour_debut');
    const demiJourFin = document.getElementById('demi_jour_fin');
    const periodeDebut = document.getElementById('periode_debut');
    const periodeFin = document.getElementById('periode_fin');
    const calculJours = document.getElementById('calculJours');
    const nbJours = document.getElementById('nbJours');

    // Enable/disable period selectors
    demiJourDebut.addEventListener('change', function() {
        periodeDebut.disabled = !this.checked;
    });

    demiJourFin.addEventListener('change', function() {
        periodeFin.disabled = !this.checked;
    });

    // Calculate number of days
    function updateDaysCount() {
        if (dateDebut.value && dateFin.value) {
            try {
                const start = new Date(dateDebut.value);
                const end = new Date(dateFin.value);

                if (!isNaN(start) && !isNaN(end) && end >= start) {
                    // Calculate days difference (+1 because inclusive)
                    const diffTime = end - start;
                    let diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

                    if (demiJourDebut.checked) {
                        diffDays -= 0.5;
                    }

                    if (demiJourFin.checked) {
                        diffDays -= 0.5;
                    }

                    nbJours.textContent = diffDays;
                    calculJours.classList.remove('hidden');
                } else {
                    calculJours.classList.add('hidden');
                }
            } catch (e) {
                calculJours.classList.add('hidden');
            }
        } else {
            calculJours.classList.add('hidden');
        }
    }

    // Format date inputs to make sure they're in ISO format (YYYY-MM-DD)
    function formatDateInput(input) {
        input.addEventListener('input', function(e) {
            // Check if the entered value matches the French formats
            const frenchDatePattern1 = /^(\d{2})\/(\d{2})\/(\d{4})$/; // DD/MM/YYYY
            const frenchDatePattern2 = /^(\d{2})-(\d{2})-(\d{4})$/; // DD-MM-YYYY
            const value = e.target.value;

            if (frenchDatePattern1.test(value)) {
                // Convert from DD/MM/YYYY to YYYY-MM-DD
                const matches = value.match(frenchDatePattern1);
                const formattedDate = `${matches[3]}-${matches[2]}-${matches[1]}`;
                e.target.value = formattedDate;
            } else if (frenchDatePattern2.test(value)) {
                // Convert from DD-MM-YYYY to YYYY-MM-DD
                const matches = value.match(frenchDatePattern2);
                const formattedDate = `${matches[3]}-${matches[2]}-${matches[1]}`;
                e.target.value = formattedDate;
            }
        });
    }

    // Apply date formatting to date inputs
    formatDateInput(dateDebut);
    formatDateInput(dateFin);

    // Add blur event to handle manual entries
    dateDebut.addEventListener('blur', function() {
        // Try to convert from DD/MM/YYYY format when losing focus
        const value = this.value;
        if (value && value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
            const parts = value.split(/[\/\-]/);
            if (parts.length === 3) {
                this.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
            }
        }
    });

    dateFin.addEventListener('blur', function() {
        // Try to convert from DD/MM/YYYY format when losing focus
        const value = this.value;
        if (value && value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
            const parts = value.split(/[\/\-]/);
            if (parts.length === 3) {
                this.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
            }
        }
    });

    // Add event listeners for date changes
    dateDebut.addEventListener('change', function() {
        updateDaysCount();
        validateLeaveRequest();
    });
    dateFin.addEventListener('change', function() {
        updateDaysCount();
        validateLeaveRequest();
    });

    // Add validation on type change
    const typeSelect = document.getElementById('type');
    if (typeSelect) {
        typeSelect.addEventListener('change', validateLeaveRequest);
    }

    // Initialize if values are already set
    updateDaysCount();

    // Validation state
    let validationTimeout;
    let isValidating = false;
    let lastValidationResult = null;

    // Function to validate leave request via AJAX
    function validateLeaveRequest() {
        const type = typeSelect ? typeSelect.value : '';
        const startDate = dateDebut.value;
        const endDate = dateFin.value;

        // Clear previous timeout
        if (validationTimeout) {
            clearTimeout(validationTimeout);
        }

        // Hide validation alert initially
        hideValidationAlert();

        // Only validate if we have all required fields
        if (!type || !startDate || !endDate) {
            return;
        }

        // Debounce validation calls
        validationTimeout = setTimeout(() => {
            performValidation(type, startDate, endDate);
        }, 500);
    }

    // Perform the actual validation
    function performValidation(type, startDate, endDate) {
        if (isValidating) return;

        isValidating = true;

        const requestData = {
            type: type,
            date_debut: startDate,
            date_fin: endDate,
            demi_journee: demiJourDebut.checked || demiJourFin.checked,
            demi_type: getDemiType()
        };

        fetch('/demandes/validate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            lastValidationResult = data;

            if (!data.valid) {
                showValidationAlert(data.errors, data.conflicts);
            } else {
                hideValidationAlert();
            }
        })
        .catch(error => {
            console.error('Validation error:', error);
            // Don't show error to user for validation failures
        })
        .finally(() => {
            isValidating = false;
        });
    }

    // Get demi_type value
    function getDemiType() {
        if (demiJourDebut.checked && demiJourFin.checked) {
            return periodeDebut.value + ',' + periodeFin.value;
        } else if (demiJourDebut.checked) {
            return periodeDebut.value;
        } else if (demiJourFin.checked) {
            return periodeFin.value;
        }
        return null;
    }

    // Show validation alert
    function showValidationAlert(errors, conflicts) {
        const alertContainer = document.getElementById('validationAlert');
        const messageElement = document.getElementById('validationMessage');
        const conflictsList = document.getElementById('conflictsList');
        const conflictsContainer = document.getElementById('conflictsContainer');

        if (!alertContainer || !messageElement) return;

        // Set main error message
        messageElement.textContent = errors.length > 0 ? errors[0] : 'Erreur de validation';

        // Handle conflicts
        if (conflicts && conflicts.length > 0) {
            conflictsContainer.innerHTML = '';

            conflicts.forEach(conflict => {
                const conflictDiv = document.createElement('div');
                conflictDiv.className = 'bg-red-50 border border-red-200 rounded p-3';

                const reference = conflict.reference_demande || `REF-${conflict.id}`;
                const startDate = conflict.date_debut_formatted || conflict.date_debut;
                const endDate = conflict.date_fin_formatted || conflict.date_fin;
                const duration = conflict.duree || 1;
                const type = conflict.type_formatted || conflict.type;
                const status = conflict.statut_formatted || conflict.statut;

                conflictDiv.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="font-medium">Référence: ${reference}</span>
                            <span class="text-sm text-red-600 ml-2">(${status})</span>
                        </div>
                        <span class="text-sm text-red-600">${type}</span>
                    </div>
                    <div class="text-sm text-red-600 mt-1">
                        Du ${startDate} au ${endDate} (${duration} jour${duration > 1 ? 's' : ''})
                    </div>
                `;

                conflictsContainer.appendChild(conflictDiv);
            });

            conflictsList.classList.remove('hidden');
        } else {
            conflictsList.classList.add('hidden');
        }

        alertContainer.classList.remove('hidden');
    }

    // Hide validation alert
    function hideValidationAlert() {
        const alertContainer = document.getElementById('validationAlert');
        if (alertContainer) {
            alertContainer.classList.add('hidden');
        }
    }

    // Submit form validation
    document.querySelector('form').addEventListener('submit', function(event) {
        try {
            // Check if there are validation conflicts
            if (lastValidationResult && !lastValidationResult.valid) {
                event.preventDefault();
                showValidationAlert(lastValidationResult.errors, lastValidationResult.conflicts);

                // Scroll to the validation alert
                const alertContainer = document.getElementById('validationAlert');
                if (alertContainer) {
                    alertContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                return;
            }

            // At this point, all dates should be in YYYY-MM-DD format due to our conversion handlers
            const startDate = new Date(dateDebut.value);
            const endDate = new Date(dateFin.value);

            if (isNaN(startDate) || isNaN(endDate)) {
                event.preventDefault();
                alert(
                    'Format de date invalide. Veuillez utiliser le format JJ/MM/AAAA ou AAAA-MM-JJ.');

                // Try to automatically fix the format if possible
                if (dateDebut.value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
                    const parts = dateDebut.value.split(/[\/\-]/);
                    if (parts.length === 3) {
                        dateDebut.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
                    }
                }

                if (dateFin.value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
                    const parts = dateFin.value.split(/[\/\-]/);
                    if (parts.length === 3) {
                        dateFin.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
                    }
                }
            } else if (endDate < startDate) {
                event.preventDefault();
                alert('La date de fin doit être postérieure ou égale à la date de début.');
            }
        } catch (e) {
            event.preventDefault();
            alert('Format de date invalide. Veuillez utiliser le format JJ/MM/AAAA ou AAAA-MM-JJ.');
        }
    });

    // File upload handling (only if elements exist)
    // const fileInput = document.getElementById('justificatif');
    // const fileNameSpan = document.getElementById('file-name');

    // if (fileInput && fileNameSpan) {
    //     fileInput.addEventListener('change', function() {
    //         if (this.files && this.files.length > 0) {
    //             const file = this.files[0];

    //             // Check file size (max 5MB)
    //             if (file.size > 5 * 1024 * 1024) {
    //                 alert('Le fichier est trop volumineux. La taille maximale est de 5 MB.');
    //                 this.value = "";
    //                 fileNameSpan.textContent = 'Aucun fichier sélectionné';
    //                 return;
    //             }

    //             // Display file name
    //             fileNameSpan.textContent = file.name;
    //         } else {
    //             fileNameSpan.textContent = 'Aucun fichier sélectionné';
    //         }
    //     });
    // }
});

// Policy Modal Functions
function openPolicyModal() {
    const modal = document.getElementById('policyModal');
    const modalCard = modal.querySelector('.modal-card');

    if (modal && modalCard) {
        modal.classList.remove('hidden');

        // Trigger animation after a small delay to ensure the modal is visible
        setTimeout(() => {
            modalCard.classList.remove('scale-95', 'opacity-0');
            modalCard.classList.add('scale-100', 'opacity-100');
        }, 10);

        // Prevent body scroll when modal is open
        document.body.style.overflow = 'hidden';
    }
}

function closePolicyModal() {
    const modal = document.getElementById('policyModal');
    const modalCard = modal.querySelector('.modal-card');

    if (modal && modalCard) {
        // Start closing animation
        modalCard.classList.remove('scale-100', 'opacity-100');
        modalCard.classList.add('scale-95', 'opacity-0');

        // Hide modal after animation completes
        setTimeout(() => {
            modal.classList.add('hidden');
            document.body.style.overflow = ''; // Restore body scroll
        }, 300);
    }
}

// Close modal when clicking outside of it
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('policyModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closePolicyModal();
            }
        });
    }

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.getElementById('policyModal');
            if (modal && !modal.classList.contains('hidden')) {
                closePolicyModal();
            }
        }
    });
});
