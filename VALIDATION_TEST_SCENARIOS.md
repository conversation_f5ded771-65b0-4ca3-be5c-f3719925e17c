# Leave Request Validation - Test Scenarios

## Test Cases for Overlap Detection

### 1. **Exact Period Match**
```
Existing Request: 2024-01-15 to 2024-01-20 (Status: Approved)
New Request:      2024-01-15 to 2024-01-20
Expected Result:  ❌ BLOCKED - Exact match conflict
```

### 2. **Partial Overlap - Start Within Existing**
```
Existing Request: 2024-01-15 to 2024-01-20 (Status: Pending Responsable)
New Request:      2024-01-18 to 2024-01-25
Expected Result:  ❌ BLOCKED - Start date overlaps
```

### 3. **Partial Overlap - End Within Existing**
```
Existing Request: 2024-01-15 to 2024-01-20 (Status: Pending Planificateur)
New Request:      2024-01-10 to 2024-01-17
Expected Result:  ❌ BLOCKED - End date overlaps
```

### 4. **Complete Encompassing - New Contains Existing**
```
Existing Request: 2024-01-17 to 2024-01-19 (Status: Approved)
New Request:      2024-01-15 to 2024-01-22
Expected Result:  ❌ BLOCKED - New request encompasses existing
```

### 5. **Complete Encompassing - Existing Contains New**
```
Existing Request: 2024-01-15 to 2024-01-25 (Status: Approved)
New Request:      2024-01-18 to 2024-01-20
Expected Result:  ❌ BLOCKED - Existing encompasses new
```

### 6. **No Overlap - Before Existing**
```
Existing Request: 2024-01-15 to 2024-01-20 (Status: Approved)
New Request:      2024-01-10 to 2024-01-14
Expected Result:  ✅ ALLOWED - No overlap
```

### 7. **No Overlap - After Existing**
```
Existing Request: 2024-01-15 to 2024-01-20 (Status: Approved)
New Request:      2024-01-21 to 2024-01-25
Expected Result:  ✅ ALLOWED - No overlap
```

### 8. **Adjacent Dates - No Overlap**
```
Existing Request: 2024-01-15 to 2024-01-20 (Status: Approved)
New Request:      2024-01-21 to 2024-01-25
Expected Result:  ✅ ALLOWED - Adjacent but not overlapping
```

## Status-Based Test Cases

### 9. **Rejected Request - Allow Resubmission**
```
Existing Request: 2024-01-15 to 2024-01-20 (Status: Rejected)
New Request:      2024-01-15 to 2024-01-20
Expected Result:  ✅ ALLOWED - Rejected requests don't block
```

### 10. **Cancelled Request - Allow Resubmission**
```
Existing Request: 2024-01-15 to 2024-01-20 (Status: Cancelled)
New Request:      2024-01-15 to 2024-01-20
Expected Result:  ✅ ALLOWED - Cancelled requests don't block
```

### 11. **Multiple Conflicts**
```
Existing Request 1: 2024-01-15 to 2024-01-17 (Status: Approved)
Existing Request 2: 2024-01-19 to 2024-01-21 (Status: Pending)
New Request:        2024-01-16 to 2024-01-20
Expected Result:    ❌ BLOCKED - Multiple conflicts detected
```

## Edge Cases

### 12. **Same Day Request**
```
Existing Request: 2024-01-15 to 2024-01-15 (Status: Approved)
New Request:      2024-01-15 to 2024-01-15
Expected Result:  ❌ BLOCKED - Same day conflict
```

### 13. **Half-Day Conflicts**
```
Existing Request: 2024-01-15 Morning (Status: Approved)
New Request:      2024-01-15 Full Day
Expected Result:  ❌ BLOCKED - Half-day conflicts with full day
```

### 14. **Different Users - No Conflict**
```
User A Request: 2024-01-15 to 2024-01-20 (Status: Approved)
User B Request: 2024-01-15 to 2024-01-20
Expected Result: ✅ ALLOWED - Different users can have same dates
```

## Frontend Validation Tests

### 15. **Real-time Validation**
- **Action**: User selects overlapping dates
- **Expected**: Immediate error display without form submission
- **Timing**: Within 500ms of date selection

### 16. **Conflict Details Display**
- **Action**: Validation detects conflict
- **Expected**: Reference number, dates, and status shown
- **Format**: "Référence: REF-2024-001 du 15/01/2024 au 20/01/2024 (Statut: Approuvée)"

### 17. **Form Submission Prevention**
- **Action**: User attempts to submit form with conflicts
- **Expected**: Form submission blocked, scroll to error message
- **Behavior**: No page reload, error remains visible

## API Endpoint Tests

### 18. **Authentication Required**
```
Request: POST /demandes/validate (without session)
Expected: 401 Unauthorized
```

### 19. **Invalid Date Format**
```
Request: POST /demandes/validate
Body: {"date_debut": "invalid", "date_fin": "2024-01-20", "type": "payé"}
Expected: 400 Bad Request with date format error
```

### 20. **Missing Required Fields**
```
Request: POST /demandes/validate
Body: {"date_debut": "2024-01-15"}
Expected: 400 Bad Request with missing fields error
```

## Database Query Tests

### 21. **SQL Injection Prevention**
```
Input: date_debut = "2024-01-15'; DROP TABLE demandes_conges; --"
Expected: Properly escaped, no SQL injection
```

### 22. **Performance Test**
```
Scenario: User with 100+ existing requests
Expected: Validation completes within 200ms
```

## Error Message Tests

### 23. **Single Conflict Message**
```
Expected Format: "Impossible de soumettre cette demande. Une demande de congé existe déjà pour cette période : [REF-2024-001] du 15/01/2024 au 20/01/2024 (Statut: Approuvée)"
```

### 24. **Multiple Conflicts Message**
```
Expected: Multiple conflict entries displayed
Format: Each conflict shown separately with full details
```

## Browser Compatibility Tests

### 25. **JavaScript Disabled**
- **Scenario**: User submits form without JavaScript
- **Expected**: Server-side validation catches conflicts
- **Behavior**: Page reload with error message

### 26. **Mobile Device**
- **Scenario**: User on mobile device
- **Expected**: Touch-friendly error display
- **Behavior**: Responsive conflict information

## Integration Tests

### 27. **End-to-End Workflow**
1. User selects conflicting dates
2. Real-time validation shows error
3. User corrects dates
4. Validation clears
5. Form submission succeeds

### 28. **Multi-User Scenario**
1. User A submits request (approved)
2. User B attempts overlapping request
3. System blocks User B's request
4. User A cancels request
5. User B can now submit same dates

## Performance Benchmarks

### 29. **Database Query Performance**
- **Target**: < 50ms for overlap detection
- **Load**: Up to 1000 existing requests per user
- **Optimization**: Proper indexing on date columns

### 30. **Frontend Response Time**
- **Target**: < 500ms for AJAX validation
- **Debouncing**: 500ms delay for user input
- **Caching**: Validation results cached during session

## Success Criteria

### ✅ **Functional Requirements**
- All overlap scenarios correctly detected
- Proper status filtering (exclude rejected/cancelled)
- Accurate error messages with conflict details

### ✅ **Performance Requirements**
- Validation completes within 500ms
- No impact on form usability
- Efficient database queries

### ✅ **User Experience Requirements**
- Immediate feedback on conflicts
- Clear conflict resolution guidance
- Accessible error messages

### ✅ **Security Requirements**
- Authentication enforced
- SQL injection prevented
- XSS protection in error display

## Test Execution Checklist

- [ ] Database validation logic
- [ ] API endpoint functionality
- [ ] Frontend real-time validation
- [ ] Form submission prevention
- [ ] Error message display
- [ ] Conflict details formatting
- [ ] Status-based filtering
- [ ] Edge case handling
- [ ] Performance benchmarks
- [ ] Security validation
- [ ] Browser compatibility
- [ ] Mobile responsiveness
- [ ] Accessibility compliance

## Automated Testing Commands

```bash
# Backend unit tests
php tests/ValidationTest.php

# Frontend integration tests
npm run test:validation

# API endpoint tests
curl -X POST /demandes/validate -H "Content-Type: application/json" -d '{"date_debut":"2024-01-15","date_fin":"2024-01-20","type":"payé"}'

# Performance tests
ab -n 100 -c 10 http://localhost/demandes/validate
```

This comprehensive test suite ensures the validation system works correctly across all scenarios and provides reliable conflict detection for leave requests.
