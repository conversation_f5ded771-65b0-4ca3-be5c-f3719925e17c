<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Historique des demandes - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_responsable.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Historique des demandes</h1>
                    <p class="text-gray-600">Suivi des demandes traitées</p>
                </div>
                <a href="/demandes-approbation" class="text-indigo-600 hover:text-indigo-800 flex items-center">
                    <i class="fas fa-clipboard-list mr-1"></i> Voir les demandes en attente
                </a>
            </div>
        </header>

        <!-- Filters -->
        <div class="card mb-6">
            <form action="/responsable/historique_demandes" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="period" class="block text-sm font-medium text-gray-700 mb-1">Période</label>
                    <select id="period" name="period" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                        <option value="">Toutes les périodes</option>
                        <option value="30" <?= $currentPeriod == '30' ? 'selected' : '' ?>>30 derniers jours</option>
                        <option value="90" <?= $currentPeriod == '90' ? 'selected' : '' ?>>3 derniers mois</option>
                        <option value="180" <?= $currentPeriod == '180' ? 'selected' : '' ?>>6 derniers mois</option>
                        <option value="365" <?= $currentPeriod == '365' ? 'selected' : '' ?>>12 derniers mois</option>
                    </select>
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                    <select id="status" name="status" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                        <option value="">Tous les statuts</option>
                        <option value="approved" <?= $currentStatus == 'approved' ? 'selected' : '' ?>>Approuvées</option>
                        <option value="rejected" <?= $currentStatus == 'rejected' ? 'selected' : '' ?>>Rejetées</option>
                    </select>
                </div>
                <div>
                    <label for="employee" class="block text-sm font-medium text-gray-700 mb-1">Employé</label>
                    <select id="employee" name="employee" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                        <option value="">Tous les employés</option>
                        <?php foreach ($teamMembers as $member): ?>
                            <option value="<?= $member['id'] ?>" <?= $currentEmployee == $member['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($member['prenom'] . ' ' . $member['nom']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-filter mr-1"></i> Filtrer
                    </button>
                    <a href="/responsable/historique_demandes" class="ml-2 inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-times mr-1"></i> Réinitialiser
                    </a>
                </div>
            </form>
        </div>

        <?php if (empty($demandes)): ?>
            <div class="bg-white rounded-lg shadow-sm p-8 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 mb-4">
                    <i class="fas fa-history text-gray-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun historique trouvé</h3>
                <p class="text-gray-500">Il n'y a pas encore d'historique de demandes pour les critères sélectionnés.</p>
            </div>
        <?php else: ?>
            <div class="bg-white shadow-sm rounded-lg overflow-hidden">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-800">Demandes traitées</h2>
                    <p class="text-sm text-gray-600 mt-1"><?= count($demandes) ?> demandes trouvées</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employé</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date décision</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($demandes as $demande): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                                <span class="text-indigo-800 font-medium"><?= strtoupper(substr($demande['prenom'], 0, 1) . substr($demande['nom'], 0, 1)) ?></span>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($demande['prenom'] . ' ' . $demande['nom']) ?></div>
                                                <div class="text-sm text-gray-500"><?= htmlspecialchars($demande['departement']) ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $typeClass = 'bg-green-100 text-green-800';
                                        switch (strtolower($demande['type'])) {
                                            case 'congé sans solde':
                                                $typeClass = 'bg-yellow-100 text-yellow-800';
                                                break;
                                            case 'congé maladie':
                                                $typeClass = 'bg-red-100 text-red-800';
                                                break;
                                            case 'congé familial':
                                                $typeClass = 'bg-blue-100 text-blue-800';
                                                break;
                                            case 'congé exceptionnel':
                                                $typeClass = 'bg-purple-100 text-purple-800';
                                                break;
                                        }
                                        ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $typeClass ?>"><?= htmlspecialchars($demande['type']) ?></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= date('d/m/Y', strtotime($demande['date_debut'])) ?>
                                            <?php if ($demande['date_debut'] != $demande['date_fin']): ?>
                                                - <?= date('d/m/Y', strtotime($demande['date_fin'])) ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= $demande['nbJours'] ?> jour<?= $demande['nbJours'] > 1 ? 's' : '' ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $statusClass = 'bg-green-100 text-green-800';
                                        if ($demande['statut'] === 'Rejetée') {
                                            $statusClass = 'bg-red-100 text-red-800';
                                        }
                                        ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $statusClass ?>">
                                            <?= $demande['statut'] ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= date('d/m/Y', strtotime($demande['date_decision'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="details-btn text-indigo-600 hover:text-indigo-900"
                                           data-employee="<?= htmlspecialchars($demande['prenom'] . ' ' . $demande['nom'], ENT_QUOTES, 'UTF-8') ?>"
                                           data-type="<?= htmlspecialchars($demande['type'], ENT_QUOTES, 'UTF-8') ?>"
                                           data-date-debut="<?= htmlspecialchars(date('d/m/Y', strtotime($demande['date_debut'])), ENT_QUOTES, 'UTF-8') ?>"
                                           data-date-fin="<?= htmlspecialchars(date('d/m/Y', strtotime($demande['date_fin'])), ENT_QUOTES, 'UTF-8') ?>"
                                           data-duree="<?= htmlspecialchars($demande['nbJours'], ENT_QUOTES, 'UTF-8') ?>"
                                           data-motif="<?= htmlspecialchars($demande['motif'] ?? 'Non spécifié', ENT_QUOTES, 'UTF-8') ?>"
                                           data-status="<?= htmlspecialchars($demande['statut'], ENT_QUOTES, 'UTF-8') ?>"
                                           data-reference="<?= htmlspecialchars($demande['reference_demande'] ?? '', ENT_QUOTES, 'UTF-8') ?>"
                                           data-decision-date="<?= htmlspecialchars($demande['date_decision_formatted'] ?? '', ENT_QUOTES, 'UTF-8') ?>"
                                           <?php if ($demande['statut'] === 'Rejetée' && isset($demande['rejection_details'])): ?>
                                           data-is-rejected="true"
                                           data-rejected-by="<?= htmlspecialchars($demande['rejection_details']['rejected_by_name'], ENT_QUOTES, 'UTF-8') ?>"
                                           data-rejected-by-role="<?= htmlspecialchars($demande['rejection_details']['rejected_by_role'], ENT_QUOTES, 'UTF-8') ?>"
                                           data-rejection-reason="<?= htmlspecialchars($demande['rejection_details']['rejection_reason'], ENT_QUOTES, 'UTF-8') ?>"
                                           data-rejection-date="<?= htmlspecialchars($demande['rejection_details']['rejection_date'], ENT_QUOTES, 'UTF-8') ?>"
                                           <?php else: ?>
                                           data-is-rejected="false"
                                           <?php endif; ?>>
                                            <i class="fas fa-eye"></i> Détails
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Modal for details -->
    <div id="detailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Détails de la demande</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="space-y-4">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-medium text-gray-800 mb-2">Informations générales</h4>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <p class="text-sm text-gray-500">Référence</p>
                            <p id="modal-reference" class="font-medium"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Employé</p>
                            <p id="modal-employee" class="font-medium"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Type de congé</p>
                            <p id="modal-type" class="font-medium"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Période</p>
                            <p id="modal-period" class="font-medium"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Durée</p>
                            <p id="modal-duration" class="font-medium"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Statut</p>
                            <p id="modal-status" class="font-medium"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Date de décision</p>
                            <p id="modal-decision-date" class="font-medium"></p>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-medium text-gray-800 mb-2">Motif de la demande</h4>
                    <p id="modal-motif" class="text-gray-700"></p>
                </div>

                <!-- Rejection Details Section (conditionally displayed) -->
                <div id="rejection-details-section" class="bg-red-50 border border-red-200 p-4 rounded-lg" style="display: none;">
                    <h4 class="font-medium text-red-800 mb-3 flex items-center">
                        <i class="fas fa-times-circle mr-2"></i>
                        Informations sur le rejet
                    </h4>
                    <div class="grid grid-cols-1 gap-3">
                        <div>
                            <p class="text-sm text-red-600">Rejetée par</p>
                            <p id="modal-rejected-by" class="font-medium text-red-800"></p>
                        </div>
                        <div>
                            <p class="text-sm text-red-600">Date de rejet</p>
                            <p id="modal-rejection-date" class="font-medium text-red-800"></p>
                        </div>
                        <div>
                            <p class="text-sm text-red-600">Motif du rejet</p>
                            <p id="modal-rejection-reason" class="text-red-700 bg-red-100 p-2 rounded border"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-6 flex justify-end space-x-3">
                <button onclick="closeModal()" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Fermer
                </button>
            </div>
        </div>
    </div>

    <script>
        // Event delegation for details buttons
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners to all details buttons
            document.querySelectorAll('.details-btn').forEach(function(button) {
                button.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Get data from data attributes
                    const employee = this.dataset.employee || '';
                    const type = this.dataset.type || '';
                    const dateDebut = this.dataset.dateDebut || '';
                    const dateFin = this.dataset.dateFin || '';
                    const duree = this.dataset.duree || '';
                    const motif = this.dataset.motif || 'Non spécifié';
                    const status = this.dataset.status || '';
                    const reference = this.dataset.reference || '';
                    const decisionDate = this.dataset.decisionDate || '';

                    // Rejection details
                    const isRejected = this.dataset.isRejected === 'true';
                    const rejectedBy = this.dataset.rejectedBy || '';
                    const rejectedByRole = this.dataset.rejectedByRole || '';
                    const rejectionReason = this.dataset.rejectionReason || '';
                    const rejectionDate = this.dataset.rejectionDate || '';

                    showDetails(employee, type, dateDebut, dateFin, duree, motif, status, reference, decisionDate, {
                        isRejected,
                        rejectedBy,
                        rejectedByRole,
                        rejectionReason,
                        rejectionDate
                    });
                });
            });
        });

        function showDetails(employee, type, dateDebut, dateFin, duree, motif, status, reference, decisionDate, rejectionDetails) {
            console.log('showDetails called with:', {employee, type, dateDebut, dateFin, duree, motif, status, reference, decisionDate, rejectionDetails});

            try {
                // Fill basic information
                document.getElementById('modal-employee').textContent = employee;
                document.getElementById('modal-type').textContent = type;
                document.getElementById('modal-period').textContent = dateDebut + (dateDebut !== dateFin ? ' - ' + dateFin : '');
                document.getElementById('modal-duration').textContent = duree + ' jour' + (parseInt(duree) > 1 ? 's' : '');
                document.getElementById('modal-motif').textContent = motif || 'Aucun motif spécifié';
                document.getElementById('modal-status').textContent = status;
                document.getElementById('modal-decision-date').textContent = decisionDate || 'Non disponible';

                // Update reference if element exists
                const referenceElement = document.getElementById('modal-reference');
                if (referenceElement) {
                    referenceElement.textContent = reference || '-';
                }

                // Handle rejection details
                const rejectionSection = document.getElementById('rejection-details-section');
                if (rejectionDetails && rejectionDetails.isRejected) {
                    // Show rejection details
                    document.getElementById('modal-rejected-by').textContent =
                        rejectionDetails.rejectedBy + ' (' + rejectionDetails.rejectedByRole + ')';
                    document.getElementById('modal-rejection-date').textContent = rejectionDetails.rejectionDate;
                    document.getElementById('modal-rejection-reason').textContent = rejectionDetails.rejectionReason;

                    rejectionSection.style.display = 'block';
                } else {
                    // Hide rejection details for non-rejected requests
                    rejectionSection.style.display = 'none';
                }

                document.getElementById('detailsModal').classList.remove('hidden');
            } catch (error) {
                console.error('Error in showDetails:', error);
                alert('Erreur lors de l\'affichage des détails. Veuillez réessayer.');
            }
        }

        function closeModal() {
            document.getElementById('detailsModal').classList.add('hidden');
        }

        // Enhanced modal functionality with click-outside support
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('detailsModal');

            // Click-outside functionality is now handled by the global modal manager
            // The modal manager will automatically detect this modal and add the functionality

            // Escape key functionality (also handled by modal manager, but keeping for compatibility)
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                    closeModal();
                }
            });
        });
    </script>
</body>
</html>
