# Leave Request Validation Implementation

## Overview
Implemented comprehensive validation logic to prevent duplicate leave requests for overlapping periods. The system now validates both on the frontend (JavaScript) and backend (PHP) to ensure data integrity and provide immediate user feedback.

## Conflict Detection Rules

### 1. **Exact Period Match**
- The new request has identical start and end dates to an existing request
- SQL: `(date_debut = ? AND date_fin = ?)`

### 2. **Overlapping Periods** 
- The new request's date range overlaps with any existing request (partial or complete overlap)
- SQL: `(? BETWEEN date_debut AND date_fin) OR (? BETWEEN date_debut AND date_fin)`

### 3. **Encompassing Periods**
- The new request completely encompasses an existing request or vice versa
- SQL: `(? <= date_debut AND ? >= date_fin) OR (date_debut <= ? AND date_fin >= ?)`

## Existing Request Statuses Checked

### ✅ **Included Statuses:**
- `approuvee` (approved requests)
- `en_attente_responsable` (pending manager approval)
- `en_attente_planificateur` (pending planner approval)

### ❌ **Excluded Statuses:**
- `refusee` (rejected requests)
- `annulee` (cancelled requests)

**Rationale**: Only active and pending requests should block new submissions. Rejected or cancelled requests should not prevent resubmission for the same period.

## Implementation Components

### 1. **Database Layer** (`app/models/DemandeModel.php`)

#### New Methods:

**`checkOverlappingRequests($userId, $startDate, $endDate, $excludeRequestId = null)`**
- Performs comprehensive overlap detection using complex SQL queries
- Returns array of conflicting requests with formatted details
- Supports exclusion of specific request ID (for updates)

**`validateLeaveRequest($userId, $startDate, $endDate, $type, $demiJournee, $demiType, $excludeRequestId = null)`**
- Main validation method that orchestrates all validation rules
- Returns structured validation result with conflicts and error messages
- Extensible for additional business rules

#### Enhanced `createDemande()` Method:
- **Before**: Simple database insertion
- **After**: Validation-first approach with structured error handling
- **Returns**: Detailed result object with success status, errors, and conflicts

### 2. **Controller Layer** (`app/controllers/DemandeController.php`)

#### Enhanced `nouvelle()` Method:
- Updated to handle new validation response format
- Improved error handling with conflict details
- Better user feedback with specific conflict information

#### New `validateRequest()` API Endpoint:
- **Route**: `/demandes/validate`
- **Method**: POST (JSON)
- **Purpose**: Real-time AJAX validation for frontend
- **Features**:
  - JSON request/response handling
  - Authentication verification
  - Date format validation
  - Business rule validation
  - Overlap conflict detection

### 3. **Frontend Layer** (`public/assets/js/nouvelle_demande.js`)

#### New Validation Features:

**Real-time Validation:**
- Debounced AJAX calls (500ms delay)
- Triggered on date/type changes
- Non-blocking user experience

**Conflict Display:**
- Dynamic conflict list generation
- Detailed conflict information (reference, dates, status, type)
- Visual indicators and styling

**Form Submission Protection:**
- Pre-submission validation check
- Prevents submission if conflicts exist
- Smooth scrolling to error messages

#### JavaScript Functions Added:
```javascript
validateLeaveRequest()      // Main validation orchestrator
performValidation()         // AJAX validation execution
showValidationAlert()       // Conflict display management
hideValidationAlert()       // Alert cleanup
getDemiType()              // Half-day type extraction
```

### 4. **View Layer Enhancements**

#### Updated Forms:
- **Employee Form**: `app/views/demandes/nouvelle.php`
- **Responsable Form**: `app/views/responsable/nouvelle_demande.php`
- **Planificateur Form**: `app/views/planificateur/nouvelle_demande.php`

#### New UI Components:

**Validation Alert Container:**
```html
<div id="validationAlert" class="hidden mb-6">
    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4">
        <div class="flex items-center">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <div>
                <p id="validationMessage" class="font-medium"></p>
                <div id="conflictsList" class="mt-3 hidden">
                    <p class="font-semibold mb-2">Demandes en conflit :</p>
                    <div id="conflictsContainer" class="space-y-2"></div>
                </div>
            </div>
        </div>
    </div>
</div>
```

**Enhanced Error Display:**
- Server-side conflict details in error messages
- Structured conflict information display
- Reference numbers, dates, and status indicators

### 5. **Routing** (`public/index.php`)

#### New Route Added:
```php
$router->addRoute('/demandes/validate', 'DemandeController', 'validateRequest');
```

## Error Message Format

### Standard Format:
```
"Impossible de soumettre cette demande. Une demande de congé existe déjà pour cette période : [Reference] du [start_date] au [end_date] (Statut: [status])"
```

### Example:
```
"Impossible de soumettre cette demande. Une demande de congé existe déjà pour cette période : [REF-2024-001] du 15/01/2024 au 20/01/2024 (Statut: Approuvée)"
```

## Edge Cases Handled

### 1. **Same-Day Requests**
- Proper handling of single-day leave requests
- Conflict detection for identical single dates

### 2. **Half-Day Conflicts**
- Detection of half-day overlaps with full-day requests
- Proper handling of morning/afternoon conflicts

### 3. **Multi-Day Spanning**
- Complex overlap detection for requests spanning multiple days
- Partial overlap detection (start/end within existing periods)

### 4. **Date Format Validation**
- Frontend and backend date format verification
- Graceful handling of invalid date inputs

### 5. **Missing Data Handling**
- Proper fallbacks for missing reference numbers
- Default values for incomplete conflict information

## User Experience Features

### 1. **Real-Time Feedback**
- Immediate validation as user types/selects dates
- Non-intrusive conflict notifications
- Visual conflict details with reference information

### 2. **Progressive Enhancement**
- Works without JavaScript (server-side validation)
- Enhanced experience with JavaScript enabled
- Graceful degradation for older browsers

### 3. **Accessibility**
- Screen reader compatible error messages
- Keyboard navigation support
- High contrast error indicators

### 4. **Mobile Responsiveness**
- Touch-friendly conflict display
- Responsive error message layout
- Mobile-optimized validation alerts

## Security Considerations

### 1. **Authentication**
- All validation endpoints require user authentication
- User ID verification for conflict checking

### 2. **Input Sanitization**
- SQL injection prevention through prepared statements
- XSS protection in error message display

### 3. **Rate Limiting**
- Debounced AJAX calls prevent spam
- Server-side validation as final authority

## Performance Optimizations

### 1. **Database Queries**
- Optimized overlap detection queries
- Proper indexing on date and user_id columns
- Minimal data retrieval for validation

### 2. **Frontend Optimization**
- Debounced validation calls
- Cached validation results
- Efficient DOM manipulation

### 3. **Caching Strategy**
- Validation results cached during session
- Reduced redundant database calls

## Testing Scenarios

### 1. **Conflict Detection**
- ✅ Exact date matches
- ✅ Partial overlaps (start/end within existing)
- ✅ Complete encompassing (new contains existing)
- ✅ Reverse encompassing (existing contains new)

### 2. **Status Filtering**
- ✅ Approved requests block new submissions
- ✅ Pending requests block new submissions
- ✅ Rejected requests allow resubmission
- ✅ Cancelled requests allow resubmission

### 3. **Edge Cases**
- ✅ Same-day requests
- ✅ Half-day conflicts
- ✅ Multi-day spanning
- ✅ Invalid date formats
- ✅ Missing data scenarios

## Files Modified

### Backend:
1. `app/models/DemandeModel.php` - Core validation logic
2. `app/controllers/DemandeController.php` - API endpoint and enhanced handling
3. `public/index.php` - New route registration

### Frontend:
4. `public/assets/js/nouvelle_demande.js` - Real-time validation
5. `app/views/demandes/nouvelle.php` - Employee form enhancements
6. `app/views/responsable/nouvelle_demande.php` - Responsable form enhancements
7. `app/views/planificateur/nouvelle_demande.php` - Planificateur form enhancements

## Expected Outcomes

### ✅ **Data Integrity**
- Elimination of duplicate leave requests
- Consistent conflict detection across all user roles
- Reliable overlap prevention

### ✅ **User Experience**
- Immediate feedback on potential conflicts
- Clear conflict resolution guidance
- Reduced form submission errors

### ✅ **System Reliability**
- Robust validation at multiple layers
- Graceful error handling
- Consistent behavior across interfaces

### ✅ **Maintainability**
- Clean, well-documented code
- Extensible validation framework
- Consistent error handling patterns

The implementation provides comprehensive protection against overlapping leave requests while maintaining excellent user experience and system performance.
