# Responsable Historical Leave Requests - Rejection Details Enhancement

## Overview
Enhanced the responsable's historical leave requests details modal at `/responsable/historique_demandes` to display complete rejection information when viewing details of rejected requests.

## Changes Made

### 1. Database Model Updates (`app/models/DemandeModel.php`)

#### Enhanced `getDemandesHistoryForResponsable()` method:
- **Added JOINs**: Include responsable and planificateur rejector information
- **New fields**: Added rejection details fields to the query
- **Enhanced formatting**: Added rejection details processing for rejected requests

#### New helper method added:
- `formatRejectionDetails($demande)` - Formats rejection information for display

#### Database Query Enhancement:
```sql
-- Before
SELECT d.*, u.nom, u.prenom, u.departement, ...
FROM demandes_conges d
JOIN users u ON d.user_id = u.id

-- After  
SELECT d.*, u.nom, u.prenom, u.departement, ...,
       resp_rejector.nom as responsable_rejector_nom,
       resp_rejector.prenom as responsable_rejector_prenom,
       plan_rejector.nom as planificateur_rejector_nom,
       plan_rejector.prenom as planificateur_rejector_prenom
FROM demandes_conges d
JOIN users u ON d.user_id = u.id
LEFT JOIN users resp_rejector ON d.responsable_id = resp_rejector.id
LEFT JOIN users plan_rejector ON d.planificateur_id = plan_rejector.id
```

#### New data fields in response:
- `rejection_details` - Complete rejection information object containing:
  - `rejected_by_name` - Full name of the person who rejected
  - `rejected_by_role` - Role of the rejector (Responsable/Planificateur)
  - `rejection_reason` - Reason provided for rejection
  - `rejection_date` - Formatted date of rejection
- `date_decision_formatted` - Formatted decision date for display

### 2. View Updates (`app/views/responsable/historique_demandes.php`)

#### Enhanced Data Attributes:
Added new data attributes to the details buttons for rejected requests:
- `data-decision-date` - Decision date information
- `data-is-rejected` - Boolean flag for rejection status
- `data-rejected-by` - Name of the person who rejected
- `data-rejected-by-role` - Role of the rejector
- `data-rejection-reason` - Rejection reason
- `data-rejection-date` - Rejection date

#### Enhanced Modal Content:
- **Added decision date field**: Shows when the decision was made
- **Enhanced motif section**: Clarified as "Motif de la demande"
- **New rejection details section**: Conditionally displayed red-themed section with:
  - Visual indicator with red border and background
  - "Rejetée par" field showing rejector name and role
  - "Date de rejet" field showing rejection timestamp
  - "Motif du rejet" field with highlighted rejection reason

#### Visual Enhancements:
- **Red color scheme**: Used for rejection information to distinguish from regular content
- **Icons**: Added `fas fa-times-circle` icon for rejection section
- **Styling**: Red borders, backgrounds, and text colors for rejection details
- **Conditional display**: Rejection section only appears for rejected requests

#### Enhanced JavaScript:
- Updated event listeners to capture rejection data attributes
- Enhanced `showDetails()` function to handle rejection information
- Conditional display logic for rejection details section
- Improved error handling and logging

### 3. Rejection Details Logic

#### Rejector Identification:
```php
// Determine who rejected the request
if (!empty($demande['responsable_rejector_nom'])) {
    // Rejected by responsable
    $rejectionDetails['rejected_by_role'] = 'Responsable';
} elseif (!empty($demande['planificateur_rejector_nom'])) {
    // Rejected by planificateur  
    $rejectionDetails['rejected_by_role'] = 'Planificateur';
} else {
    // Unknown rejector (fallback)
    $rejectionDetails['rejected_by_role'] = 'Rôle inconnu';
}
```

#### Fallback Handling:
- **Empty rejection reason**: Shows "Aucun motif spécifié"
- **Missing rejector info**: Shows "Utilisateur inconnu"
- **Missing dates**: Shows "Date inconnue"

### 4. User Experience Improvements

#### For Rejected Requests:
- ✅ Complete visibility into rejection decision
- ✅ Clear identification of who made the rejection
- ✅ Specific rejection reason provided
- ✅ Timestamp of rejection decision
- ✅ Visual distinction with red color scheme

#### For Non-Rejected Requests:
- ✅ Standard modal display (no rejection section)
- ✅ All existing functionality preserved
- ✅ Enhanced with decision date information

#### Conditional Display:
- **Rejected requests**: Show full rejection details section
- **Approved/Pending requests**: Hide rejection section completely
- **Responsive design**: Maintains mobile compatibility

## Technical Implementation Details

### Database Relationships:
- `demandes_conges.responsable_id` → `users.id` (for responsable rejections)
- `demandes_conges.planificateur_id` → `users.id` (for planificateur rejections)
- Uses LEFT JOINs to handle cases where rejector info might be missing

### Data Flow:
1. **Database Query**: Retrieves rejection details with user information
2. **Model Processing**: Formats rejection data using `formatRejectionDetails()`
3. **View Rendering**: Adds rejection data as HTML data attributes
4. **JavaScript Handling**: Extracts and displays rejection information in modal

### Error Handling:
- Graceful handling of missing rejection information
- Fallback text for incomplete data
- Console logging for debugging
- User-friendly error messages

## Files Modified:
1. `app/models/DemandeModel.php` - Database query and data formatting
2. `app/views/responsable/historique_demandes.php` - UI enhancements and JavaScript

## Expected Outcome:
Responsables now have complete visibility into rejection decisions for their team members' leave requests, including:
- **Who rejected** the request (name and role)
- **When** the rejection occurred (date and time)
- **Why** the request was rejected (specific reason)
- **Visual distinction** to easily identify rejection information

This enhancement provides transparency in the approval process and helps responsables understand and communicate rejection decisions to their team members effectively.
